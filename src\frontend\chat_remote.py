from api.agent_server import create_thread, search_threads, delete_thread, run_stream_from_message, SSEParser
from uuid import UUID
from colorama import Fore, Style
import random
import nest_asyncio
nest_asyncio.apply()


async def main():
    user_id = UUID("00000000-0000-0000-0000-000000000000")
    
    
    memory_session_id = str(random.choice(range(100))) + "-" + str(random.choice(range(100))) + "-" + str(random.choice(range(100))) + "-" + str(random.choice(range(100)))
    print
    try:
        thread_id = await create_thread(user_id)
        print(f"\nCreated thread: {thread_id}")

        threads = await search_threads(user_id)
        print(f"\nFound threads: {threads}")

        configurable = {
            "thread_id": str(thread_id),
            "table_id": "tbl_c19353f2f59e4786"
        }

    

        # Create a persistent parser to track seen messages across the conversation
        parser = SSEParser()

        user_input = "Briefly introduce yourself and offer to help me"
        while True:
            print(f"\n ---- 🚀 AI Agent CLI ---- \n")
            # print(f"Starting stream with assistant_id: test, thread_id: {thread_id}")
            # print(f"Message: {user_input}")
            # print(f"Configurable: {configurable}")

            result_count = 0
            thinking_started = False
            response_started = False
            tool_started = False
            async for result in run_stream_from_message(
                thread_id=thread_id,
                #assistant_id="bond_ai",
                assistant_id="outbond_ai_assistant_v3",
                message=user_input,
                configurable=configurable,
                parser=parser,
            ):
                result_count += 1
                #print(f"[DEBUG] Result #{result_count}: {repr(result)}")
                try:
                    # Handle list of thinking blocks or other structured data
                    if isinstance(result, list):
                        for item in result:
                            if isinstance(item, dict):
                                # Handle thinking blocks
                                if item.get('type') == 'thinking':
                                    thinking_text = item.get('thinking', '')
                                    if thinking_text:
                                        # Show thinking indicator only once at the start
                                        if not thinking_started:
                                            print(f"\n{Fore.YELLOW}AI IS THINKING{Style.RESET_ALL}")
                                            thinking_started = True
                                        print(Fore.YELLOW + thinking_text + Style.RESET_ALL, end="", flush=True)
                                # Handle tool use blocks
                                elif item.get('type') == 'tool_use':
                                    # Show tool name when tool starts
                                    if 'name' in item and not tool_started:
                                        tool_name = item.get('name', 'unknown')
                                        print(f"\n{Fore.MAGENTA}🔧 Using tool: {tool_name}{Style.RESET_ALL}")
                                        tool_started = True
                                    
                                    # Show partial JSON as it builds
                                    partial_json = item.get('partial_json', '')
                                    if partial_json:
                                        print(Fore.MAGENTA + partial_json + Style.RESET_ALL, end="", flush=True)
                                # Handle text chunks
                                elif item.get('type') == 'text':
                                    text_content = item.get('text', '')
                                    if text_content:
                                        # Start response on new line when transitioning from thinking/tools
                                        if not response_started:
                                            print(f"\n")  # New line before response
                                            response_started = True
                                            tool_started = False  # Reset tool flag for next tool use
                                        print(Fore.CYAN + text_content + Style.RESET_ALL, end="", flush=True)
                                # Handle other dict types
                                else:
                                    content = item.get('content', str(item))
                                    print(Fore.CYAN + content + Style.RESET_ALL, end="", flush=True)
                            elif isinstance(item, str):
                                print(Fore.CYAN + item + Style.RESET_ALL, end="", flush=True)
                    elif isinstance(result, str):
                        # Handle tool response messages (like "✅ **Tool Response: search**")
                        if "Tool Response:" in result:
                            print(f"\n{Fore.GREEN}{result}{Style.RESET_ALL}")
                            tool_started = False  # Reset tool flag after tool completes
                        else:
                            print(Fore.CYAN + result + Style.RESET_ALL, end="", flush=True)
                    else:
                        print(Fore.CYAN + str(result) + Style.RESET_ALL, end="", flush=True)
                except Exception as e:
                    print(f"Error processing result: {e}, result type: {type(result)}, result: {result}")

            # Reset flags for next message
            thinking_started = False
            response_started = False
            tool_started = False

            # print(f"[DEBUG] Total results received: {result_count}")
            # if result_count == 0:
                # print("[DEBUG] No results received from stream!")

            user_input = input("\n\nUser ('exit' to quit): ")
            if user_input.lower() in ["exit", "quit"]:
                print("\n\nExit command received. Exiting...\n\n")
                break
            print(f"\n\n ----- 🥷 User ----- \n\n{user_input}\n")

        # Clean up
        await delete_thread(thread_id)
        print(f"\nDeleted thread: {thread_id}")
    except Exception as e:
        print(f"Error: {type(e).__name__}: {str(e)}")
        raise
    
if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
