


def debug_messages(messages, location=""):
    """Debug helper to trace message structure"""
    print(f"\n=== DEBUG MESSAGES {location} ===")
    for i, msg in enumerate(messages):
        print(f"Message {i}:")
        print(f"  Type: {type(msg)}")
        print(f"  Content: {getattr(msg, 'content', 'No content')[:200]}...")
        if hasattr(msg, 'tool_calls'):
            print(f"  Tool calls: {msg.tool_calls}")
        if hasattr(msg, 'thinking'):
            print(f"  Thinking: {getattr(msg, 'thinking', None)}")
        print(f"  Full dict: {json.dumps(msg.__dict__ if hasattr(msg, '__dict__') else str(msg), indent=2, default=str)}")
    print("=== END DEBUG ===\n")
    
    
def _msg_to_text(m) -> str:
    # role
    role = m.__class__.__name__.replace("Message", "").upper()
    name = getattr(m, "name", None)
    # content (reuse your cleaner)
    try:
        content = _extract_clean_content(m)
    except Exception:
        content = str(getattr(m, "content", ""))
    # tool calls (if any)
    tool_calls = []
    if hasattr(m, "tool_calls") and m.tool_calls:
        for tc in m.tool_calls:
            fn = (tc.get("function") or {}).get("name") or tc.get("name")
            tool_calls.append(fn or "unknown")
    elif isinstance(getattr(m, "additional_kwargs", {}), dict):
        tc_kw = m.additional_kwargs.get("tool_calls")
        if isinstance(tc_kw, list):
            for tc in tc_kw:
                fn = (tc.get("function") or {}).get("name") or tc.get("name")
                tool_calls.append(fn or "unknown")
    suffix = f"  → tool_calls: {', '.join(tool_calls)}" if tool_calls else ""
    head = f"{role}{f'[{name}]' if name else ''}"
    return f"{head}: {content}{suffix}"

def _log_agent_io(agent_name: str, tag: str, messages: list):
    print(f"\n--- [{agent_name}] {tag} ({len(messages)} msg) ---")
    for i, m in enumerate(messages, start=1):
        print(f"{i:02d}. {_msg_to_text(m)}")
    print(f"--- end {tag} ---\n")
