"""LinkedIn company profile column creation tool."""

from typing import Optional, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langgraph.config import get_stream_writer
from bond_ai.configuration import Configuration
import json

from ..agent_db import upsert_smart_column
from src.outbond_ai_assistant_v2.supabase.validate_injection_sequance.validate_injection_sequance import validate_injection_sequence
from src.outbond_ai_assistant_v2.supabase.validate_injection_sequance.models import ValidateInjectionSequenceRequest, TargetDataType


@tool
def upsert_linkedin_company_profile_column_from_url(
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    linkedin_company_url: str,
    column_id: Optional[int] = None,
) :
    """Create a new LinkedIn company column or update an existing one using company URL.

    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.
    Parameters:
        config: Configuration injected by the system
        column_name: A short descriptive name of the column. Must be unique per table.
        linkedin_company_url: A SINGLE injection path of the LinkedIn company URL data point.
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.
        
    Returns:
        Tuple[Optional[str], Optional[str]]: Tuple containing (success_message, error_message)
        where success_message is a confirmation if successful, None if failed
        and error_message is the error description if failed, None if successful
    """
    try:
        
        # any injection_sequence must be wrapped in double curly braces {{}}
        # the llm now return without the braces, so we add them here
        linkedin_profile_url = f"{{{{{linkedin_profile_url}}}}}"
        print(f"\n[Debug] upsert_linkedin_company_profile_column_from_url Column name: {column_name}, linkedin_company_url: {linkedin_company_url}, column_id: {column_id}\n")
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 8

                        # Step 1: Validate injection sequence
        validation_request = ValidateInjectionSequenceRequest(
            injection_sequence=linkedin_company_url,
            target_data_type=TargetDataType.LINKEDIN_COMPANY_URL
        )

        validation_result = validate_injection_sequence(config=config, request=validation_request)
        
        # If validation failed, return the validation response directly
        if not validation_result.success:
            return validation_result
        
        
        inputs = [
            {"linkedin_company_url": linkedin_company_url}
        ]
        
        # Set up providers as specified in the payload
        providers = [
            {"providers": ["outbond"]}
        ]
        
        # Set up parameters (empty dict as in the payload)
        parameters = [{}]
        
        # Call the create_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
        if error:
            return None, error
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "message": f"The LinkedIn company column was successfully {action}. Column name: '{name}' Column ID: {column_id}, Column Type: {column_type}"
        }
        
        return json.dumps(response_json), None
        
    except Exception as e:
        return None, str(e)
