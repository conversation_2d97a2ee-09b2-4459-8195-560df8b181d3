"""Shared Pydantic models for tools."""

from typing import Optional, List, Union
from pydantic import BaseModel, Field
from enum import Enum


class Operator(str, Enum):
    """Filter operators for table data queries."""
    EQ = "eq"
    """Equal to"""
    
    NEQ = "neq"
    """Not equal to"""
    
    LT = "lt"
    """Less than"""
    
    LTE = "lte"
    """Less than or equal to"""
    
    GT = "gt"
    """Greater than"""
    
    GTE = "gte"
    """Greater than or equal to"""
    
    CONTAINS = "contains"
    """Contains substring"""
    
    NCONTAINS = "ncontains"
    """Does not contain substring"""
    
    EMPTY = "empty"
    """Is empty"""
    
    NEMPTY = "nempty"
    """Is not empty"""
    
    ERROR = "error"
    """Has error from a runnable column"""
    
    NERROR = "nerror"
    """Does not have error from a runnable column"""
    
    RESULT = "result"
    """Has result from a runnable column"""
    
    NRESULT = "nresult"
    """Does not have result from a runnable column"""
    
    RUN = "run"
    """Is running"""
    
    NRUN = "nrun"
    """Is not running"""
    
    AWAITING_INPUT = "awaiting_input"
    """Awaiting input"""
    
    QUEUED = "queued"
    """Is queued"""
    
    FAILED = "failed"
    """Has failed"""


class GroupOperator(str, Enum):
    """Operators for combining filter rules."""
    AND = "AND"
    """All conditions must be true"""
    
    OR = "OR"
    """At least one condition must be true"""


class Filter(BaseModel):
    """Filter model for table data queries."""
    column_id: int = Field(
        ..., 
        description="ID of the column to filter. The IDs are 1-indexed, not 0-indexed."
    )
    operator: Operator = Field(
        ..., 
        description="Filter operator such as 'eq', 'neq', 'lt', 'lte', etc."
    )
    value: Optional[str] = Field(
        None, 
        description="Value to compare against. Not required for some operators like 'empty'."
    )


class FilterGroup(BaseModel):
    """Group of filters that can be combined with AND/OR operators.
    
    Supports nested filter groups for complex query conditions.
    """
    operator: GroupOperator = Field(
        ..., 
        description="Logical operator ('AND' or 'OR') to combine the rules in this group."
    )
    rules: List[Union['Filter', 'FilterGroup']] = Field(
        ..., 
        description="List of filters or nested filter groups to apply. For nested groups, each can have its own operator."
    )
    
    class Config:
        use_enum_values = True


class Sort(BaseModel):
    """Sort model for table data queries."""
    column_id: int = Field(
        ..., 
        description="ID of the column to sort by. The IDs are 1-indexed, not 0-indexed."
    )
    direction: str = Field(
        ..., 
        description="Sort direction, either 'asc' (ascending) or 'desc' (descending)."
    )

    class Config:
        use_enum_values = True


class RunStatus(BaseModel):
    """Model for run status in cell schema."""
    run: Optional[str] = Field(None, description="The run status (e.g., 'completed', 'failed', 'pending') or null")
    message: Optional[str] = Field(None, description="Optional message about the run status")


class CellDetails(BaseModel):
    """Model for complex cell details with nested schema."""
    # This will contain the nested schema fields dynamically
    class Config:
        extra = "allow"  # Allow additional fields for the nested schema


class ColumnSummary(BaseModel):
    """Model for individual column summary in table data."""
    column_id: int = Field(description="The unique identifier for the column")
    column_name: str = Field(description="The display name of the column")
    is_runnable: bool = Field(description="Whether this column can be executed/run")
    cell_value: Optional[str] = Field(None, description="Schema type for primitive values (e.g., 'string', 'integer', 'boolean', 'null', 'array of <type>')")
    cell_details: Optional[CellDetails] = Field(None, description="Nested schema structure for complex objects/arrays")
    run_status: Optional[RunStatus] = Field(None, description="Run status information for runnable columns")
    data_summary: str = Field(description="Human-readable summary of the data patterns and content")
    
    class Config:
        # Ensure exactly one of cell_value or cell_details is present
        validate_assignment = True


class TableSummaryOutput(BaseModel):
    """Model for the complete table summary structured output."""
    columns: List[ColumnSummary] = Field(description="Array of column summaries")
