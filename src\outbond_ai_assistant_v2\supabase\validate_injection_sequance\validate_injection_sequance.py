"""Database validation operations for injection sequence validation."""

import re
from typing import List, Any, Optional
from ..client import supabase
from .models import ValidateInjectionSequenceRequest, ValidateInjectionSequenceResponse, TargetDataType
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from typing import Annotated
from ...configuration import Configuration


def _validate_linkedin_profile_url(url: str) -> bool:
    """Validate if a URL is a valid LinkedIn profile URL."""
    if not isinstance(url, str):
        return False
    
    # LinkedIn profile URL regex pattern - check if linkedin.com/in/ is contained in the text
    linkedin_pattern = r'linkedin\.com/in/'
    return bool(re.search(linkedin_pattern, url.strip()))


def _validate_linkedin_company_url(url: str) -> bool:
    """Validate if a URL is a valid LinkedIn company URL."""
    if not isinstance(url, str):
        return False
    
    # LinkedIn company URL regex pattern - check if linkedin.com/company is contained in the text
    linkedin_company_pattern = r'linkedin\.com/company'
    return bool(re.search(linkedin_company_pattern, url.strip()))


def _validate_full_name(name: str) -> bool:
    """Validate if a string is a full name (at least 2 words with space in between)."""
    if not isinstance(name, str):
        return False
    
    # Remove extra whitespace and split
    name_parts = name.strip().split()
    
    # Must have at least 2 parts (first name and last name)
    if len(name_parts) < 2:
        return False
    
    return True


def _validate_domain(domain: str) -> bool:
    """Validate if a string is a valid domain name."""
    if not isinstance(domain, str):
        return False
    
    # Remove protocol and path if present
    domain = domain.strip()
    if domain.startswith(('http://', 'https://')):
        domain = domain.split('//', 1)[1].split('/')[0]
    
    # Basic domain validation regex
    domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$'
    return bool(re.match(domain_pattern, domain))


def _validate_target_data_type_values(values: Optional[List[Any]], target_data_type: Optional[TargetDataType]) -> bool:
    """Validate if at least one value matches the expected target data type."""
    if not values or not target_data_type:
        return True  # Skip validation if no values or target_data_type specified
    
    # Ensure values is a list - if it's a string, convert it to a single-item list
    if isinstance(values, str):
        values = [values]
    
    valid_count = 0
    
    for value in enumerate(values):
        str_value = str(value)
        
        if target_data_type == TargetDataType.LINKEDIN_PROFILE_URL:
            if _validate_linkedin_profile_url(str_value):
                valid_count += 1
        elif target_data_type == TargetDataType.LINKEDIN_COMPANY_URL:
            if _validate_linkedin_company_url(str_value):
                valid_count += 1
        elif target_data_type == TargetDataType.FULL_NAME:
            if _validate_full_name(str_value):
                valid_count += 1
        elif target_data_type == TargetDataType.DOMAIN:
            if _validate_domain(str_value):
                valid_count += 1
    
    return valid_count > 0


def validate_injection_sequence(
    config: Annotated[RunnableConfig, InjectedToolArg],
    request: ValidateInjectionSequenceRequest
) -> ValidateInjectionSequenceResponse:
    """Validate an injection sequence against a table."""
    try:
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id

        response = supabase.rpc('validate_injection_sequence', {
            "injection_sequence": request.injection_sequence,
            "table_id": table_id,
        }).execute()

        
        # Success case: RPC returns data with values
        if hasattr(response, 'data') and response.data and 'values' in response.data:
            values = response.data.get('values', [])

            print(" [Debug] validate_injection_sequence")
            print("values", values)
            print("values type:", type(values))
            print("values length:", len(values) if hasattr(values, '__len__') else 'no length')
            print(" [Debug]")
            # If target_data_type is specified, validate the values
            if request.target_data_type:
                if not _validate_target_data_type_values(values, request.target_data_type):
                    # Generate target_data_type-specific error message
                    if request.target_data_type == TargetDataType.LINKEDIN_PROFILE_URL:
                        error_msg = "The injection sequence is correct. However the values you referred to inside of the column are not valid LinkedIn profile URLs"
                    elif request.target_data_type == TargetDataType.LINKEDIN_COMPANY_URL:
                        error_msg = "The injection sequence is correct. However the values you referred to inside of the column are not valid LinkedIn company URLs"
                    elif request.target_data_type == TargetDataType.FULL_NAME:
                        error_msg = "The injection sequence is correct. However the values you referred to inside of the column are not valid full names (must be 2 words separated by space)"
                    elif request.target_data_type == TargetDataType.DOMAIN:
                        error_msg = "The injection sequence is correct. However the values you referred to inside of the column are not valid domain names"
                    else:
                        error_msg = f"The injection sequence is correct. However the values you referred to inside of the column are not valid {request.target_data_type} values"
                    
                    return ValidateInjectionSequenceResponse(
                        injection_sequence=request.injection_sequence,
                        error_message=error_msg,
                        success=False
                    )
            
            return ValidateInjectionSequenceResponse(
                success=True
            )
        
        # Unexpected response format
        return ValidateInjectionSequenceResponse(
            injection_sequence=request.injection_sequence,
            error_message=f"Unexpected response from validation service for injection sequence '{request.injection_sequence}'",
            success=False
        )
                    
    except Exception as error:
        # Extract database error message
        error_str = str(error)
        return ValidateInjectionSequenceResponse(
            injection_sequence=request.injection_sequence,
            error_message=f"Validation failed for injection sequence '{request.injection_sequence}': {error_str}",
            success=False
        )

