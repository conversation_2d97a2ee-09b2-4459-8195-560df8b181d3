# Async Mode Usage Guide

This document explains how to use the async functionality in the Bond AI system.

## Overview

The Bond AI system now supports both synchronous and asynchronous execution modes:

- **Sync Mode**: Traditional sequential execution (default)
- **Async Mode**: Concurrent execution with potential for parallel task processing

## Available Graphs

### 1. Synchronous Graph
```python
from src.agents.bond_ai.graph import graph

# Use the sync graph for traditional execution
result = graph.invoke(state, config)
```

### 2. Asynchronous Graph
```python
from src.agents.bond_ai.graph import async_graph

# Use the async graph for concurrent execution
result = await async_graph.ainvoke(state, config)
```

### 3. Dynamic Graph Selection
```python
from src.agents.bond_ai.graph import get_graph

# Get the appropriate graph based on preference
sync_graph = get_graph(async_mode=False)
async_graph = get_graph(async_mode=True)
```

## LangGraph Configuration

The async graph is also exposed in `langgraph.json`:

```json
{
  "graphs": {
    "bond_ai": "./src/agents/bond_ai/graph.py:graph",
    "bond_ai_async": "./src/agents/bond_ai/graph.py:async_graph"
  }
}
```

## Key Differences

### Sync Mode
- Sequential agent execution
- Blocking I/O operations
- Simpler error handling
- Lower resource usage

### Async Mode
- Concurrent agent execution
- Non-blocking I/O operations
- Potential for parallel task processing
- Better scalability for I/O-heavy operations

## Implementation Details

### Async Agent Creation
The system includes async versions of agent creation functions:

- `_create_async_react_agent()`: Creates async ReAct agents
- `_create_async_react_agent_with_state_injection()`: Creates async ReAct agents with state injection

### Async Supervisor
The `supervisor_agent_anthropic()` function is fully async and includes:

- Async task dispatching
- Concurrent task execution capabilities
- Non-blocking LLM calls

### Parallel Task Execution
The async system includes helper functions for parallel execution:

- `_find_parallel_executable_tasks()`: Identifies tasks that can run concurrently
- `_execute_parallel_tasks()`: Executes multiple tasks concurrently
- `_merge_parallel_results()`: Combines results from parallel execution

## Usage Examples

### Basic Async Usage
```python
import asyncio
from src.agents.bond_ai.graph import async_graph
from src.agents.bond_ai.state import BondAIWorkflowState

async def run_async_workflow():
    state = BondAIWorkflowState(
        messages=[{"content": "Hello, please help me with research"}]
    )
    
    result = await async_graph.ainvoke(state)
    return result

# Run the async workflow
result = asyncio.run(run_async_workflow())
```

### Streaming with Async
```python
async def stream_async_workflow():
    state = BondAIWorkflowState(
        messages=[{"content": "Hello, please help me with research"}]
    )
    
    async for chunk in async_graph.astream(state):
        print(f"Received chunk: {chunk}")

# Run streaming
asyncio.run(stream_async_workflow())
```

## Benefits of Async Mode

1. **Better Performance**: For I/O-heavy operations (web scraping, API calls, LLM requests)
2. **Concurrent Execution**: Multiple independent agents can run simultaneously
3. **Improved User Experience**: Faster response times for complex workflows
4. **Scalability**: Better resource utilization for high-load scenarios
5. **Future-Proof**: Ready for more sophisticated parallel processing

## When to Use Async Mode

Use async mode when:
- Your workflow involves multiple independent tasks
- You have I/O-heavy operations (web scraping, API calls)
- You need better performance for complex workflows
- You want to take advantage of concurrent execution

Use sync mode when:
- You have simple, sequential workflows
- You prefer simpler error handling
- You don't need the performance benefits of async execution

## Error Handling

Both modes include proper error handling:
- Error response nodes for user-friendly error messages
- Graceful fallbacks from async to sync execution
- Proper state management during errors
- Fixed async supervisor routing logic to prevent infinite loops

## Async/Sync Compatibility

### ICP Agent Handling
The system automatically handles async/sync compatibility for complex subgraphs:

- **Sync Workflow**: Uses `icp_agent_wrapper_sync()` with synchronous `invoke()`
- **Async Workflow**: Uses `icp_agent_wrapper()` with asynchronous `ainvoke()`

This ensures that subgraphs containing async nodes (like the ICP perplexity graph) work correctly in both execution modes.

### Custom Node Considerations
When adding custom nodes that contain async operations:

1. **For Sync Workflows**: Ensure nodes use synchronous calls
2. **For Async Workflows**: Ensure nodes use async calls (`await`, `ainvoke`, etc.)
3. **For Mixed Compatibility**: Create separate sync/async versions like the ICP agent

### Subgraph Visibility in LangStudio

**Problem**: When subgraphs are wrapped in adapter functions, LangStudio can't detect them as subgraphs.

**Solution**: The system now uses direct subgraph imports with state transformation:

```python
# Direct import preserves LangStudio visibility
from bond_ai.perplexity.graph import icp_persona_graph

# State transformation functions
def transform_to_icp_state(state): ...
def transform_from_icp_result(result): ...

# Wrapper calls subgraph directly
async def icp_agent_wrapper(state, config):
    icp_state = transform_to_icp_state(state)
    result = await icp_persona_graph.ainvoke(icp_state, config)  # Direct call
    return transform_from_icp_result(result)
```

**Benefits**:
- ✅ LangStudio can see subgraph structure
- ✅ Maintains async/sync compatibility
- ✅ Clean state transformation
- ✅ Preserves error handling

### React Worker Subgraph Visibility

**Problem**: React workers created with `create_react_agent` were wrapped in factory functions, hiding their internal ReAct subgraph structure.

**Solution**: Direct ReAct agent creation with wrapper preservation:

```python
def _create_react_agent(name: str, system_prompt: str, tools: list, llm=None):
    # Create ReAct agent directly (visible to LangStudio)
    react_agent = create_react_agent(
        model=llm,
        tools=tools,
        prompt=prompt,
        state_schema=BondAIWorkflowState
    )

    def agent_wrapper_node(state, config):
        # Call ReAct agent directly (preserves visibility)
        result = react_agent.invoke(cleaned_state, config)
        return formatted_result

    # Store reference for LangStudio visibility
    agent_wrapper_node._react_agent = react_agent
    return agent_wrapper_node
```

**Benefits**:
- ✅ LangStudio can see ReAct agent internal structure (agent → tools → agent loop)
- ✅ Maintains state cleaning and result formatting
- ✅ Preserves async/sync compatibility
- ✅ Shows tool usage patterns in visualization

### State Injection React Workers

**Problem**: React workers with state injection (`add_agent_prebuid_react_with_state_injection`) created ReAct agents dynamically at runtime, preventing LangStudio from detecting the subgraph structure.

**Solution**: Base agent creation with dynamic prompt injection:

```python
def _create_react_agent_with_state_injection(name, system_prompt, tools, state_injections, llm):
    # Create base ReAct agent for LangStudio visibility
    base_react_agent = create_react_agent(
        model=llm,
        tools=tools,
        prompt=base_prompt,  # Base prompt structure
        state_schema=BondAIWorkflowState
    )

    def dynamic_prompt_node(state, config):
        if not state_injections:
            # Use base agent directly
            result = base_react_agent.invoke(cleaned_state, config)
        else:
            # Create dynamic agent with injected prompt
            injected_prompt = inject_state_values(system_prompt, state, state_injections)
            dynamic_agent = create_react_agent(...)
            result = dynamic_agent.invoke(cleaned_state, config)
        return formatted_result

    # Store base agent reference for LangStudio visibility
    dynamic_prompt_node._base_react_agent = base_react_agent
    return dynamic_prompt_node
```

**Benefits**:
- ✅ LangStudio can see base ReAct agent structure
- ✅ Dynamic state injection works at runtime
- ✅ Maintains performance and flexibility
- ✅ Shows tool usage patterns for state injection agents (e.g., `linkedin_enrichment_agent`)

## Troubleshooting

### Common Issues and Solutions

**Issue**: Agent completes but supervisor invokes it again
- **Cause**: JSON parsing logic in supervisor routing
- **Solution**: Fixed in async supervisor routing logic (v1.1+)

**Issue**: "No synchronous function provided" error
- **Cause**: Async nodes being called with sync methods
- **Solution**: Use async graph (`bond_ai_async`) for async operations

**Issue**: Subgraph compatibility errors
- **Cause**: Mixed async/sync node calls
- **Solution**: Ensure proper async/sync wrapper functions (like ICP agent)

**Issue**: React workers not showing as subgraphs in LangStudio
- **Cause**: ReAct agents wrapped in factory functions
- **Solution**: Fixed with direct ReAct agent creation and reference preservation (v1.2+)

**Issue**: State injection React workers not visible as subgraphs
- **Cause**: ReAct agents created dynamically at runtime in state injection functions
- **Solution**: Fixed with base agent creation and dynamic prompt injection (v1.3+)

## Future Enhancements

The async system is designed to support future enhancements:
- More sophisticated parallel task analysis
- Dynamic load balancing
- Advanced concurrency patterns
- Performance monitoring and optimization
