"""Define a ReAct agent using LangGraph.

This agent follows the ReAct pattern (Reasoning and Acting) to solve tasks
by thinking step by step and using tools when needed.
"""


import functools
import logging
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, BaseMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from pydantic import BaseModel

from bond_ai.utilities.debug import _log_agent_io, debug_messages
from bond_ai.nodes import table_indexing_node, planner_agent
from bond_ai.registry.registry import  SupervisorSubAgents
from bond_ai.prompts_v1 import   SUPERVISOR_AGENT_PROMPT


from bond_ai.configuration import Configuration
from bond_ai.state import  BondAIWorkflowState
from bond_ai.registry import supervisor_sub_agent_registry
from bond_ai.utils import clean_thinking_blocks_for_bedrock, load_chat_model, load_chat_model_non_thinking

from langgraph.prebuilt import create_react_agent

load_dotenv()



#                                MOVE TO SDK
# ============================================================================
# UTILITY FUNCTIONS FOR EASY AGENT MANAGEMENT
# ============================================================================

def add_agent_prebuid_react(
    name: str, 
    system_prompt: str, 
    tools: list,
    description: str,
    state_injections: dict = None,
    enabled: bool = True):
    """Add a new ReAct agent to the registry."""
    supervisor_sub_agent_registry.register_react_prebuild_agent(
        name = name,
        system_prompt = system_prompt,
        description= description, 
        tools = tools,
        enabled = enabled,
        state_injections = state_injections or {},
        )
    logging.info(f"[DEBUG] ✓ Added ReAct agent: {name}")


def add_node(name: str, node_function, description: str, enabled: bool = True, **kwargs):
    """Add a pre-built node function to the registry."""
    supervisor_sub_agent_registry.register_custom_agent(name, node_function, description,enabled, **kwargs)
    logging.info(f"[DEBUG] ✓ Added custom node: {name}")

def enable_agent(name: str):
    """Enable an agent or node."""
    supervisor_sub_agent_registry.enable_agent(name)
    print(f"[DEBUG] ✓ Enabled: {name}")

def disable_agent(name: str):
    """Disable an agent or node."""
    supervisor_sub_agent_registry.disable_agent(name)
    print(f"[DEBUG] ✓ Disabled: {name}")

def list_agents():
    """List all agents and nodes with their status."""
    print("\n=== AGENT REGISTRY ===")
    for name, config in supervisor_sub_agent_registry.agents.items():
        status = "[DEBUG] ✓ ENABLED" if config.get("enabled", True) else "✗ DISABLED"
        agent_type = config.get("type", "unknown")

        if agent_type == "react_agent":
            tools_count = len(config.get("tools", []))
            print(f"{status} | {name} | Type: ReAct Agent | Tools: {tools_count}")
        elif agent_type == "custom_node":
            print(f"{status} | {name} | Type: Custom Node | Function: {config.get('node_function', {}).get('__name__', 'Unknown')}")
        else:
            print(f"{status} | {name} | Type: {agent_type}")
    print()

def rebuild_workflow():
    """Rebuild the workflow with current registry state."""
    global graph
    logging.info("Rebuilding workflow with current agent registry...")
    current_agents = supervisor_sub_agent_registry.get_agent_names()
    logging.info(f"Active agents: {current_agents}")
    graph = create_workflow_from_registry()
    logging.info("✓ Workflow rebuilt successfully")
    return graph

def agent_node(state, agent, name):
    result = agent.invoke(state)
    return {
        "messages": [HumanMessage(content=result["messages"][-1].content, name=name)]
    }


def _create_react_agent(name: str, system_prompt: str, tools: list, state_injections: dict = None, llm=None):
    """Factory function to create a ReAct agent with optional state injection and LangStudio visibility."""
    if llm is None:
        llm = ChatOpenAI(model="gpt-4o-mini")

    # Create the prompt
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="messages")
    ])

    # Create the ReAct agent directly (visible to LangStudio)
    react_agent = create_react_agent(
        model=llm,
        tools=tools,
        prompt=prompt,
        state_schema=BondAIWorkflowState,
        debug=True
    )

    def agent_wrapper_node(state, config=None):
        """Wrapper that handles state cleaning, optional state injection, and result formatting"""
        if config is None:
            config = RunnableConfig()
        
        # Clean thinking blocks from state messages before processing and log input for specific agents
        cleaned_state = dict(state)
        debug_messages(cleaned_state["messages"], "agent_wrapper_node")
        cleaned_state["messages"] = clean_thinking_blocks_for_bedrock(list(state["messages"]))

        # Log input for specific agents
        if name == SupervisorSubAgents.linkedin_enrichment_agent.value.name:
            _log_agent_io(name, "IN", cleaned_state["messages"])

        # Handle state injection if provided
        if state_injections:
            # Inject state values into system prompt
            injected_prompt = system_prompt
            for placeholder, state_path in state_injections.items():
                value = _get_nested_state_value(state, state_path)
                escaped_value = _escape_braces_in_content(str(value))
                injected_prompt = injected_prompt.replace(f"{{{{{placeholder}}}}}", escaped_value)

            # Create dynamic prompt and agent
            dynamic_prompt = ChatPromptTemplate.from_messages([
                ("system", injected_prompt),
                MessagesPlaceholder(variable_name="messages")
            ])

            dynamic_react_agent = create_react_agent(
                model=llm,
                tools=tools,
                prompt=dynamic_prompt,
                state_schema=BondAIWorkflowState
            )

            result = dynamic_react_agent.invoke(cleaned_state, config)
        else:
            # Use base agent without state injection
            result = react_agent.invoke(cleaned_state, config)

        # Log output for specific agents
        if name == SupervisorSubAgents.linkedin_enrichment_agent.value.name:
            prev_len = len(cleaned_state["messages"])
            produced = result["messages"][prev_len:]
            _log_agent_io(name, "OUT", produced)

        # Return clean summary
        last_message = result["messages"][-1]
        summary = f"[{name}] Task completed: {_extract_clean_content(last_message)}"

        return {
            "messages": [AIMessage(content=summary, name=name)]
        }

    # Store metadata
    agent_wrapper_node._agent_name = name
    agent_wrapper_node._react_agent = react_agent
    agent_wrapper_node._system_prompt = system_prompt
    agent_wrapper_node._tools = tools
    agent_wrapper_node._has_state_injection = bool(state_injections)
    agent_wrapper_node._state_injections = state_injections or {}

    return agent_wrapper_node



def _get_nested_state_value(state, path, default=""):
    """Helper to safely extract nested values from state using dot notation."""
    try:
        value = state
        for key in path.split('.'):
            value = value.get(key) if isinstance(value, dict) else getattr(value, key, None)
        return value if value is not None else default
    except:
        return default

def _escape_braces_in_content(content: str) -> str:
    """Escape single curly braces in content to prevent LangGraph template errors."""
    # Replace single { and } with doubled versions to escape them
    return content.replace('{', '{{').replace('}', '}}')

def _extract_clean_content(message):
    """Extract clean text content from a message, avoiding thinking blocks."""
    if isinstance(message.content, list):
        text_parts = []
        for block in message.content:
            if isinstance(block, dict) and block.get("type") == "text":
                text_parts.append(block.get("text", ""))
        return " ".join(text_parts).strip() or "Task completed successfully"
    else:
        return str(message.content) or "Task completed successfully"


##########################################################################
###################### ASYNC AGENT CREATION FUNCTIONS ####################
##########################################################################

def _create_async_react_agent(name: str, system_prompt: str, tools: list, state_injections: dict = None, llm=None):
    """Factory function to create an async ReAct agent with optional state injection and LangStudio visibility."""
    if llm is None:
        llm = ChatOpenAI(model="gpt-4o-mini")

    # Create the prompt
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="messages")
    ])

    # Create the ReAct agent directly (visible to LangStudio)
    react_agent = create_react_agent(
        model=llm,
        tools=tools,
        prompt=prompt,
        state_schema=BondAIWorkflowState
    )

    async def async_agent_wrapper_node(state, config):
        """Async wrapper that handles state cleaning, optional state injection, and result formatting"""
        # Clean thinking blocks from state messages before processing
        cleaned_state = dict(state)
        cleaned_state["messages"] = clean_thinking_blocks_for_bedrock(list(state["messages"]))

        # Handle state injection if provided
        if state_injections:
            # Inject state values into system prompt
            injected_prompt = system_prompt
            for placeholder, state_path in state_injections.items():
                value = _get_nested_state_value(state, state_path)
                escaped_value = _escape_braces_in_content(str(value))
                injected_prompt = injected_prompt.replace(f"{{{{{placeholder}}}}}", escaped_value)

            # Create dynamic prompt and agent
            dynamic_prompt = ChatPromptTemplate.from_messages([
                ("system", injected_prompt),
                MessagesPlaceholder(variable_name="messages")
            ])

            dynamic_react_agent = create_react_agent(
                model=llm,
                tools=tools,
                prompt=dynamic_prompt,
                state_schema=BondAIWorkflowState
            )

            result = await dynamic_react_agent.ainvoke(cleaned_state, config)
        else:
            # Use base agent without state injection
            result = await react_agent.ainvoke(cleaned_state, config)

        # Return clean summary
        last_message = result["messages"][-1]
        summary = f"[{name}] Task completed: {_extract_clean_content(last_message)}"

        return {
            "messages": [AIMessage(content=summary, name=name)]
        }

    # Store metadata
    async_agent_wrapper_node._agent_name = name
    async_agent_wrapper_node._react_agent = react_agent
    async_agent_wrapper_node._system_prompt = system_prompt
    async_agent_wrapper_node._tools = tools
    async_agent_wrapper_node._has_state_injection = bool(state_injections)
    async_agent_wrapper_node._state_injections = state_injections or {}
    async_agent_wrapper_node._is_async = True

    return async_agent_wrapper_node


##############################  END MOVE TO SUPERVISOR SDK ##############################


llm_thinking = load_chat_model("bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0")
llm_no_thinking = load_chat_model_non_thinking("bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0")

##########################################################################
######################  custom nodes BUILDER ##########################
##########################################################################
try:
    supervisor_sub_agent_registry.register_custom_agent(
        name = SupervisorSubAgents.planner_agent.value.name,
        node_function = planner_agent,
        description = SupervisorSubAgents.planner_agent.value.description
    )
    logging.info("✓ Registered Planner node")
except ImportError as e:
    logging.error(f"⚠ Could not import planner_agent: {e}")



#The wrapper handles state conversion between schemas properly.
# Import the ICP subgraph directly for LangStudio visibility
from bond_ai.perplexity.graph import icp_persona_graph
# TODO REVIEW THE ICP STATE AND REMOVE THE TRANSFORMATION FROM SYNC AND ASYNC
# Create state transformation functions
def transform_to_icp_state(state: BondAIWorkflowState):
    """Transform BondAI state to ICP state format"""
    return {
        "messages": state["messages"],
        "steps": [],
        "answer": None
    }

def transform_from_icp_result(result):
    """Transform ICP result back to BondAI format"""
    from langchain_core.messages import AIMessage

    final_answer = result.get("answer", "ICP analysis completed successfully")
    steps_summary = f"Completed {len(result.get('steps', []))} analysis steps"
    completion_message = f"✅ ICP Analysis Complete\n\n{final_answer}\n\n{steps_summary}"

    return {
        "messages": [AIMessage(content=completion_message, name="icp_agent")]
    }

# Create async wrapper that preserves subgraph reference
async def icp_agent_wrapper(state: BondAIWorkflowState, config: RunnableConfig):
    """Async wrapper for ICP subgraph with state transformation"""
    import logging

    try:
        logging.info("🎯 Starting ICP analysis...")

        # Transform state
        icp_state = transform_to_icp_state(state)

        # Call subgraph directly (preserves LangStudio visibility)
        result = await icp_persona_graph.ainvoke(icp_state, config)

        # Transform result
        return transform_from_icp_result(result)

    except Exception as e:
        logging.error(f"❌ ICP analysis failed: {e}")
        from langchain_core.messages import AIMessage
        return {
            "messages": [AIMessage(content=f"ICP analysis encountered an error: {str(e)}", name="icp_agent")]
        }

# Create sync wrapper that preserves subgraph reference
def icp_agent_wrapper_sync(state: BondAIWorkflowState, config: RunnableConfig):
    """Sync wrapper for ICP subgraph with state transformation"""
    import logging

    try:
        logging.info("🎯 Starting ICP analysis (sync mode)...")

        # Transform state
        icp_state = transform_to_icp_state(state)

        # Call subgraph directly (preserves LangStudio visibility)
        result = icp_persona_graph.invoke(icp_state, config)

        # Transform result
        return transform_from_icp_result(result)

    except Exception as e:
        logging.error(f"❌ ICP analysis failed: {e}")
        from langchain_core.messages import AIMessage
        return {
            "messages": [AIMessage(content=f"ICP analysis encountered an error: {str(e)}", name="icp_agent")]
        }

# Register the wrapper (sync version for regular workflow)
supervisor_sub_agent_registry.register_custom_agent(
    name = SupervisorSubAgents.icp_agent.value.name,
    node_function = icp_agent_wrapper_sync,
    description = SupervisorSubAgents.icp_agent.value.description
)


##########################################################################
######################  react default nodes BUILDER ##########################
##########################################################################
# ADD REACT AGENTS TO SUPERVISOR
# BY DEFAULT ARE ALL ENABLED
# if you want to disable an agent:
# 1) pass  ENABLED = False
# 2) call disable_agent(name)

add_agent_prebuid_react(
    name= SupervisorSubAgents.build_list_agent.value.name,
    system_prompt = SupervisorSubAgents.build_list_agent.value.system_prompt,
    tools= SupervisorSubAgents.build_list_agent.value.tools,
    description=SupervisorSubAgents.build_list_agent.value.description,
   
)
add_agent_prebuid_react(
    name=SupervisorSubAgents.linkedin_enrichment_agent.value.name,
    description=SupervisorSubAgents.linkedin_enrichment_agent.value.description,
    system_prompt=SupervisorSubAgents.linkedin_enrichment_agent.value.system_prompt,
    state_injections=SupervisorSubAgents.linkedin_enrichment_agent.value.state_injections,
    tools=SupervisorSubAgents.linkedin_enrichment_agent.value.tools,
)
add_agent_prebuid_react(
    name= SupervisorSubAgents.content_agent.value.name,
    system_prompt = SupervisorSubAgents.content_agent.value.system_prompt,
    tools= SupervisorSubAgents.content_agent.value.tools,
    description= SupervisorSubAgents.content_agent.value.description
)
add_agent_prebuid_react(
    name= SupervisorSubAgents.data_management_agent.value.name,
    system_prompt = SupervisorSubAgents.data_management_agent.value.system_prompt,
    tools= SupervisorSubAgents.data_management_agent.value.tools,
    description=SupervisorSubAgents.data_management_agent.value.description
)
add_agent_prebuid_react(
    name= SupervisorSubAgents.execution_agent.value.name,
    system_prompt = SupervisorSubAgents.execution_agent.value.system_prompt,
    tools= SupervisorSubAgents.execution_agent.value.tools,
    description=SupervisorSubAgents.execution_agent.value.description
)
add_agent_prebuid_react(
    name= SupervisorSubAgents.email_phone_agent.value.name,
    system_prompt = SupervisorSubAgents.email_phone_agent.value.system_prompt,
    tools= SupervisorSubAgents.email_phone_agent.value.tools,
    description=SupervisorSubAgents.email_phone_agent.value.description,
    state_injections=SupervisorSubAgents.linkedin_enrichment_agent.value.state_injections,
    
)





##########################################################################
######################  NODES BUILDER ##########################
##########################################################################

## TODO Move node to nodes when architure is in final shape
def error_response_node(state:BondAIWorkflowState):
    """Node that handles error messages and informs the user before ending."""
    from langchain_core.messages import AIMessage

    # Get the error message from state
    error_message = state.get("last_error_message", "An unexpected error occurred.")

    # Create a user-friendly error response
    user_message = f"I encountered an issue while processing your request: {error_message}\n\nPlease try rephrasing your request or contact support if the issue persists."

    # Return the error message and set next to FINISH to end the conversation
    return {
        "messages": [AIMessage(content=user_message)],
        "next": "FINISH"
    }


def task_completion_node(state:BondAIWorkflowState):
    """Node that handles task completion reporting and updates task status."""
    from langchain_core.messages import AIMessage

    active_task_id = state.get("active_task_id")
    if not active_task_id:
        return {
            "messages": [AIMessage(content="No active task to complete.")],
            "next": "supervisor"
        }

    # Update task status to completed
    updated_state = _update_task_status_in_state(state, active_task_id, "completed")

    # Generate status summary
    status_summary = get_task_status_summary(updated_state.get("plan_tasks", []))

    return {
        "messages": [AIMessage(content=f"✅ Task {active_task_id} completed successfully.\n\n{status_summary}")],
        "plan_tasks": updated_state.get("plan_tasks", []),
        "active_task_id": None,
        "next": "supervisor"
    }



##########################################################################
###################### SUPERVISOR BUILDER ##########################
##########################################################################


async def supervisor_agent_anthropic(
    state: BondAIWorkflowState,
    config: RunnableConfig
):
    """Async supervisor with concurrent task execution"""
    configuration = Configuration.from_runnable_config(config)
    print("\n[DEBUG] INVOKED ASYNC SUPERVISOR\n")
    
    # Check for errors first
    if state.get("last_error_message"):
        return {"next": "error_response"}
    
    current_members = ["FINISH"] + ["error_response"] + supervisor_sub_agent_registry.get_agent_names()
    plan_tasks = state.get("plan_tasks", [])
    active_task_id = state.get("active_task_id")
    
    # Async task-based dispatching
    if plan_tasks:
        return await _supervisor_handle_plan_task_based_dispatching_async(
            state, plan_tasks, active_task_id, current_members, configuration.table_id
        )
    
    # Async general routing
    return await _supervisor_handle_general_routing_async(
        state, current_members, configuration.table_id
    )

async def _supervisor_handle_plan_task_based_dispatching_async(
    state, plan_tasks, active_task_id, current_members, table_id
):
    """Async version of task dispatching with potential parallelization"""

    # Find independent tasks that can run in parallel
    parallel_tasks = _find_parallel_executable_tasks(plan_tasks)

    if len(parallel_tasks) > 1:
        # Execute multiple tasks concurrently
        task_results = await _execute_parallel_tasks(parallel_tasks, state, current_members)
        return _merge_parallel_results(task_results)
    else:
        # Single task execution (existing logic)
        return _dispatch_single_task(plan_tasks, active_task_id, current_members)

async def _supervisor_handle_general_routing_async(state: BondAIWorkflowState, current_members, table_id: str):
    """Async version of general routing when no execution plan exists."""
    import re
    import json
    from datetime import datetime
    from langchain_core.messages import SystemMessage

    # Check if there's an error message in the state
    if state.get("last_error_message"):
        return {"next": "error_response"}

    # Build dynamic agent directory
    agent_directory_entries = []

    react_agents = supervisor_sub_agent_registry.get_react_agents()
    # Process ReAct agents
    for name, config in react_agents.items():
        description = config.get('description', 'ReAct agent')
        tools = config.get('tools', [])

        # Get tool names
        tool_names = []
        for tool in tools:
            if hasattr(tool, 'name'):
                tool_names.append(tool.name)
            elif hasattr(tool, '__name__'):
                tool_names.append(tool.__name__)
            else:
                tool_names.append(str(tool))

        tools_str = ', '.join(tool_names) if tool_names else 'No specific tools'
        agent_entry = f"- **{name}**: {description} (Tools: {tools_str})"
        agent_directory_entries.append(agent_entry)

    custom_nodes = supervisor_sub_agent_registry.get_custom_nodes()
    # Process custom nodes
    for name, config in custom_nodes.items():
        description = config.get('description', '')
        agent_entry = f"- **{name}**: {description}"
        agent_directory_entries.append(agent_entry)

    # Build the agent directory section
    agent_directory = f"""**AVAILABLE SPECIALIZED AGENTS:**
{chr(10).join(agent_directory_entries)}

**ROUTING OPTIONS:**
Valid choices: {', '.join(current_members)}
• Use "FINISH" when the user's request has been completely fulfilled
• Choose the team member whose expertise best matches the current need"""

    # Get context variables (with defaults for missing values)
    today_date = datetime.now().strftime('%Y-%m-%d')
    current_filters = getattr(state, 'current_filters', 'Not available')
    table_summary = state.get('table_summary', 'Not available')
    selected_row_ids = state.get('selected_row_ids', 'None')
    selected_column_ids = state.get('selected_column_ids', 'None')
    mode = state.get('mode', 'Not available')

    # decision_making_rule = """**DECISION-MAKING CRITERIA:**

    # Task Delegation Rules:
    # - **Linkedin Agent**: Linkedin query for people and companies
    # - **Research Agent**: Web search, website scraping, competitive analysis
    # - **Enrichment Agent**: Contact data discovery (emails, phones), LinkedIn profile imports, data validation
    # - **Content Agent**: AI text generation, research insights, personalized messaging, copywriting
    # - **Data Management Agent**: Table operations, filtering, data analysis, column management
    # - **Execution Agent**: Column execution, monitoring, batch operations, result tracking
    # """
    decision_making_rule = ""
    # Format the supervisor prompt with context and agent directory
    system_content = SUPERVISOR_AGENT_PROMPT.format(
        table_id=table_id,
        today_date=today_date,
        current_filters=current_filters,
        table_summary=table_summary,
        selected_row_ids=selected_row_ids,
        selected_column_ids=selected_column_ids,
        mode=mode,
        decision_making_rules=decision_making_rule, ## TODO remove agent_directory
        agent_directory=agent_directory 
    ) + f"""

**ROUTING INSTRUCTIONS:**
Based on the conversation history, determine who should act next.
Respond with a JSON object: {{"next": "AGENT_NAME"}}
where AGENT_NAME is one of: {', '.join(current_members)}"""

    # print("[DEBUG] SUPERVISOR PROMPT:", system_content)

    print("[DEBUG] SUPERVISOR INOUT PESSAGE:", list(state["messages"]))
# 
    cleaned_messages = clean_thinking_blocks_for_bedrock(list(state["messages"]))

    print("[DEBUG] SUPERVISOR cleaned_messages PESSAGE:",cleaned_messages)

    # Create messages manually instead of using ChatPromptTemplate
    messages = [SystemMessage(content=system_content)]
    messages.extend(cleaned_messages)

    # Use async invoke
    response = await llm_no_thinking.ainvoke(messages)
    
    
    print("[DEBUG] SUPERVISOR RESPONSE:", response)
    #debug_messages(response, "SUPERVISOR_INPUT")
   
    # Extract the content from the response
    if hasattr(response, 'content'):
        content = response.content
    else:
        content = str(response)

    # Handle case where content might be a list or other non-string type
    if isinstance(content, list):
        if content and isinstance(content[0], str):
            content = content[0]
        else:
            content = str(content)
    elif not isinstance(content, str):
        content = str(content)

    # Try to parse JSON from the response
    try:
        # Look for JSON in the response
        if content.strip().startswith('{') and content.strip().endswith('}'):
            parsed = json.loads(content.strip())
            next_agent = parsed.get("next")
        else:
            # Look for JSON in the response
            json_match = re.search(r'\{[^}]*"next"[^}]*\}', content)
            if json_match:
                json_str = json_match.group()
                parsed = json.loads(json_str)
                next_agent = parsed.get("next")
            else:
                next_agent = None

        if next_agent and next_agent in current_members:
            logging.info(f"Supervisor routing decision: {next_agent} (from options: {current_members})\n")
            logging.debug(f"LLM response content: {content}")
            return {"next": next_agent}

    except (json.JSONDecodeError, AttributeError):
        pass
        # Fallback parsing: look for any valid option in the response
    next_agent = None
    for option in current_members:
        if option.upper() in content.upper():
            next_agent = option
            break

    # Default fallback
    if next_agent is None or next_agent not in current_members:
        next_agent = "FINISH"

    logging.info(f"Supervisor routing decision: {next_agent} (from options: {current_members})\n")
    logging.debug(f"LLM response content: {content}")

    # Return in the same format as the original function
    return {"next": next_agent}


##########################################################################
###################### ASYNC HELPER FUNCTIONS ##########################
##########################################################################

def _find_parallel_executable_tasks(plan_tasks):
    """Find tasks that can be executed in parallel (no dependencies)."""
    if not plan_tasks:
        return []

    # Convert to list of dicts if needed
    tasks_list = []
    for task in plan_tasks:
        if hasattr(task, 'model_dump'):
            tasks_list.append(task.model_dump())
        elif isinstance(task, dict):
            tasks_list.append(task)
        else:
            task_dict = {
                'id': getattr(task, 'id', ''),
                'order': getattr(task, 'order', 0),
                'action': getattr(task, 'action', ''),
                'agent': getattr(task, 'agent', ''),
                'tool': getattr(task, 'tool', ''),
                'why': getattr(task, 'why', ''),
                'status': getattr(task, 'status', 'pending'),
                'error': getattr(task, 'error', None)
            }
            tasks_list.append(task_dict)

    # For now, return only the first pending task (simple implementation)
    # In a more sophisticated version, you could analyze dependencies
    pending_tasks = [t for t in tasks_list if t['status'] == 'pending']
    return pending_tasks[:1]  # Return max 1 task for now

async def _execute_parallel_tasks(parallel_tasks, state, current_members):
    """Execute multiple tasks concurrently."""
    import asyncio
    from langchain_core.messages import AIMessage

    if not parallel_tasks:
        return []

    # For now, just execute the first task (fallback to single execution)
    # In a full implementation, you would create concurrent executions
    task = parallel_tasks[0]

    # Check if agent is available
    if task['agent'] not in current_members:
        task['status'] = 'failed'
        task['error'] = f"Agent '{task['agent']}' not available"
        return [task]

    # Mark as in progress and return dispatch info
    task['status'] = 'in_progress'

    return [{
        'task': task,
        'result': {
            "messages": [AIMessage(content=f"🎯 **TASK ASSIGNMENT**\n\n**Task ID:** {task['id']}\n**Action:** {task['action']}\n**Tool Required:** {task['tool']}\n**Objective:** {task['why']}\n\n**Instructions:** Please execute this task using the specified tool. Focus on the action described and provide clear results.")],
            "plan_tasks": [task],
            "active_task_id": task['id'],
            "next": task['agent']
        }
    }]

def _merge_parallel_results(task_results):
    """Merge results from parallel task execution."""
    if not task_results:
        return {"next": "FINISH"}

    # For single task execution, return the first result
    if len(task_results) == 1:
        return task_results[0]['result']

    # For multiple tasks, merge messages and return to supervisor
    from langchain_core.messages import AIMessage

    all_messages = []
    all_tasks = []

    for result in task_results:
        if 'result' in result:
            result_data = result['result']
            if 'messages' in result_data:
                all_messages.extend(result_data['messages'])
            if 'plan_tasks' in result_data:
                all_tasks.extend(result_data['plan_tasks'])

    return {
        "messages": all_messages,
        "plan_tasks": all_tasks,
        "next": "supervisor"
    }

def _dispatch_single_task(plan_tasks, active_task_id, current_members):
    """Dispatch a single task (fallback from async to sync execution)."""
    # Use the existing sync implementation
    return _supervisor_handle_plan_task_based_dispatching(
        {"plan_tasks": plan_tasks, "active_task_id": active_task_id},
        plan_tasks,
        active_task_id,
        current_members,
        ""  # table_id not used in this context
    )


def _supervisor_handle_general_routing(state:BondAIWorkflowState, current_members,table_id:str):
    """Handle general routing when no execution plan exists."""
    import re
    import json
    from datetime import datetime
    from langchain_core.messages import SystemMessage

    # Check if there's an error message in the state
    if state.get("last_error_message"):
        return {"next": "error_response"}
    
 
    
    # Build dynamic agent directory
    agent_directory_entries = []
    react_agents = supervisor_sub_agent_registry.get_react_agents()
    custom_nodes = supervisor_sub_agent_registry.get_custom_nodes()

    # Process ReAct agents
    for name, config in react_agents.items():
        description = config.get('description', 'ReAct agent')
        tools = config.get('tools', [])

        # Get tool names
        tool_names = []
        for tool in tools:
            if hasattr(tool, 'name'):
                tool_names.append(tool.name)
            elif hasattr(tool, '__name__'):
                tool_names.append(tool.__name__)
            else:
                tool_names.append(str(tool))

        tools_str = ', '.join(tool_names) if tool_names else 'No specific tools'
        agent_entry = f"- **{name}**: {description} (Tools: {tools_str})"
        agent_directory_entries.append(agent_entry)

    # Process custom nodes
    for name, config in custom_nodes.items():
        description = config.get('description', 'Custom node function')
        agent_entry = f"- **{name}**: {description} (Type: Custom node function)"
        agent_directory_entries.append(agent_entry)

    # Build the agent directory section
    agent_directory = f"""**AVAILABLE SPECIALIZED AGENTS:**
{chr(10).join(agent_directory_entries)}

**ROUTING OPTIONS:**
Valid choices: {', '.join(current_members)}
• Use "FINISH" when the user's request has been completely fulfilled
• Choose the team member whose expertise best matches the current need"""

    # Get context variables (with defaults for missing values)
    table_id = table_id ## Move the table_ID to state. This will require a small change on FE side. getattr(state, 'table_id', 'Not available')
    today_date = datetime.now().strftime('%Y-%m-%d')
    current_filters = getattr(state, 'current_filters', 'Not available')
    table_summary = state.get('table_summary', 'Not available')
    selected_row_ids = state.get('selected_row_ids', 'None')
    selected_column_ids = state.get('selected_column_ids', 'None')
    mode = state.get('mode', 'Not available')


#TODO Improve registry creating a function so this will be dinamic. Also plan A/B test with and without this section that overalapp with agent_directory
    decision_making_rule = """**DECISION-MAKING CRITERIA:**

    Task Delegation Rules:
    - **Linkedin Agent**: Linkedin query for people and companies
    - **Research Agent**: Web search, website scraping, competitive analysis
    - **Enrichment Agent**: Contact data discovery (emails, phones), LinkedIn profile imports, data validation
    - **Content Agent**: AI text generation, research insights, personalized messaging, copywriting
    - **Data Management Agent**: Table operations, filtering, data analysis, column management
    - **Execution Agent**: Column execution, monitoring, batch operations, result tracking
    """


    # Format the supervisor prompt with context and agent directory
    system_content = SUPERVISOR_AGENT_PROMPT.format(
        table_id=table_id,
        today_date=today_date,
        current_filters=current_filters,
        table_summary=table_summary,
        selected_row_ids=selected_row_ids,
        selected_column_ids=selected_column_ids,
        mode=mode,
        decision_making_rules = decision_making_rule,
        agent_directory=agent_directory
    ) + f"""

**ROUTING INSTRUCTIONS:**
Based on the conversation history, determine who should act next.
Respond with a JSON object: {{"next": "AGENT_NAME"}}
where AGENT_NAME is one of: {', '.join(current_members)}"""


    #print("[Debug] Supervisosor Prompt", system_content)
    cleaned_messages = clean_thinking_blocks_for_bedrock(list(state["messages"]))

    # Create messages manually instead of using ChatPromptTemplate
    messages = [SystemMessage(content=system_content)]
    messages.extend(cleaned_messages)  # Use the already cleaned messages from _deep_clean_thinking_blocks
    # Add the conversation messages from state
  
    # if "messages" in state:
    #     messages.extend(state["messages"])

    # Invoke the LLM directly with the messages
    # TODO We are using no thinking
    response = llm_no_thinking.invoke(messages)

    # Extract the content from the response
    if hasattr(response, 'content'):
        content = response.content
    else:
        content = str(response)

    # Handle case where content might be a list or other non-string type
    if isinstance(content, list):
        if content and isinstance(content[0], str):
            content = content[0]
        else:
            content = str(content)
    elif not isinstance(content, str):
        content = str(content)

    # Try to parse JSON from the response
    try:
        # Look for JSON in the response
        if content.strip().startswith('{') and content.strip().endswith('}'):
            parsed = json.loads(content.strip())
            next_agent = parsed.get("next")
        else:
            # Look for JSON in the response
            json_match = re.search(r'\{[^}]*"next"[^}]*\}', content)
            if json_match:
                json_str = json_match.group()
                parsed = json.loads(json_str)
                next_agent = parsed.get("next")
            else:
                next_agent = None

            if next_agent and next_agent in current_members:
                logging.info(f"Supervisor routing decision: {next_agent} (from options: {current_members})\n")
                logging.debug(f"LLM response content: {content}")
                return {"next": next_agent}

    except (json.JSONDecodeError, AttributeError):
        pass
        # Fallback parsing: look for any valid option in the response
    next_agent = None
    for option in current_members:
        if option.upper() in content.upper():
                next_agent = option
                break

         # Default fallback
    if next_agent is None or next_agent not in current_members:
        next_agent = "FINISH"

    logging.info(f"Supervisor routing decision: {next_agent} (from options: {current_members})\n")
    logging.debug(f"LLM response content: {content}")

    # Return in the same format as the original function
    return {"next": next_agent}



def _supervisor_handle_plan_task_based_dispatching(state, plan_tasks, active_task_id, current_members, table_id):
    """Handle task-based dispatching when execution plan exists."""
    from langchain_core.messages import AIMessage

    # Check if there's an error message in the state
    if state.get("last_error_message"):
        return {"next": "error_response"}
    
    # Use the more aggressive cleaning function
      
    # Find the next task to execute
    next_task = None
    updated_tasks = []

    # Convert plan_tasks to list of dicts if they're Task objects
    tasks_list = []
    for task in plan_tasks:
        if hasattr(task, 'model_dump'):
            tasks_list.append(task.model_dump())
        elif isinstance(task, dict):
            tasks_list.append(task)
        else:
            # Try to convert to dict
            task_dict = {
                'id': getattr(task, 'id', ''),
                'order': getattr(task, 'order', 0),
                'action': getattr(task, 'action', ''),
                'agent': getattr(task, 'agent', ''),
                'tool': getattr(task, 'tool', ''),
                'why': getattr(task, 'why', ''),
                'status': getattr(task, 'status', 'pending'),
                'error': getattr(task, 'error', None)
            }
            tasks_list.append(task_dict)

    # Sort tasks by order
    tasks_list.sort(key=lambda x: x.get('order', 0))

    # Mark current active task as completed if it exists
    if active_task_id:
        for task in tasks_list:
            if task['id'] == active_task_id and task['status'] == 'in_progress':
                task['status'] = 'completed'
                logging.info(f"✓ Completed task: {task['id']} - {task['action']}")
                break

    # Find next pending task
    for task in tasks_list:
        if task['status'] == 'pending':
            # Check if this agent is available
            if task['agent'] in current_members:
                next_task = task
                break
            else:
                # Agent not available, mark as failed
                task['status'] = 'failed'
                task['error'] = f"Agent '{task['agent']}' not available"
                logging.warning(f"⚠ Task {task['id']} failed: Agent '{task['agent']}' not available")

    # If no next task found, check if all tasks are completed
    if not next_task:
        pending_tasks = [t for t in tasks_list if t['status'] == 'pending']
        failed_tasks = [t for t in tasks_list if t['status'] == 'failed']

        if not pending_tasks:
            # All tasks completed or failed
            completed_tasks = [t for t in tasks_list if t['status'] == 'completed']

            if failed_tasks:
                failure_summary = "\n".join([f"- {t['action']}: {t.get('error', 'Unknown error')}" for t in failed_tasks])
                message = f"Execution plan completed with {len(completed_tasks)} successful tasks and {len(failed_tasks)} failed tasks.\n\nFailed tasks:\n{failure_summary}"
            else:
                message = f"✅ Execution plan completed successfully! All {len(completed_tasks)} tasks have been executed."

            return {
                "messages": [AIMessage(content=message)],
                "plan_tasks": _convert_to_task_objects(tasks_list),
                "active_task_id": None,
                "next": "FINISH"
            }

    # Dispatch next task
    if next_task:
        # Mark task as in progress
        next_task['status'] = 'in_progress'

        # Create task delegation message
        delegation_message = f"""
🎯 **TASK ASSIGNMENT**

**Task ID:** {next_task['id']}
**Action:** {next_task['action']}
**Tool Required:** {next_task['tool']}
**Objective:** {next_task['why']}

**Instructions:** Please execute this task using the specified tool. Focus on the action described and provide clear results.
"""

        logging.info(f"🚀 Dispatching task {next_task['id']} to agent '{next_task['agent']}'")

        return {
            "messages": [AIMessage(content=delegation_message)],
            "plan_tasks": _convert_to_task_objects(tasks_list),
            "active_task_id": next_task['id'],
            "next": next_task['agent']
        }

    # Fallback - no executable tasks
    return {
        "messages": [AIMessage(content="No executable tasks found in the current plan.")],
        "plan_tasks": _convert_to_task_objects(tasks_list),
        "active_task_id": None,
        "next": "FINISH"
    }


def _convert_to_task_objects(tasks_list):
    """Convert list of task dicts back to Task objects."""
    from bond_ai.models.planner_model import Task

    task_objects = []
    for task_dict in tasks_list:
        try:
            task_obj = Task(**task_dict)
            task_objects.append(task_obj)
        except Exception as e:
            logging.warning(f"Failed to convert task dict to Task object: {e}")
            # Keep as dict if conversion fails
            task_objects.append(task_dict)

    return task_objects


def get_task_status_summary(plan_tasks):
    """Generate a summary of task execution status."""
    if not plan_tasks:
        return "No tasks in execution plan."

    # Convert to list of dicts if needed
    tasks_list = []
    for task in plan_tasks:
        if hasattr(task, 'model_dump'):
            tasks_list.append(task.model_dump())
        elif isinstance(task, dict):
            tasks_list.append(task)
        else:
            task_dict = {
                'id': getattr(task, 'id', ''),
                'action': getattr(task, 'action', ''),
                'status': getattr(task, 'status', 'pending'),
                'agent': getattr(task, 'agent', ''),
            }
            tasks_list.append(task_dict)

    # Count by status
    status_counts = {}
    for task in tasks_list:
        status = task.get('status', 'pending')
        status_counts[status] = status_counts.get(status, 0) + 1

    # Generate summary
    total_tasks = len(tasks_list)
    summary_parts = [f"📊 **Task Execution Summary** ({total_tasks} total tasks)"]

    status_emojis = {
        'completed': '✅',
        'in_progress': '🔄',
        'pending': '⏳',
        'failed': '❌'
    }

    for status, count in status_counts.items():
        emoji = status_emojis.get(status, '❓')
        summary_parts.append(f"{emoji} {status.title()}: {count}")

    return "\n".join(summary_parts)


def _update_task_status_in_state(state:BondAIWorkflowState, task_id, new_status, error_message=None):
    """Utility function to update task status in state."""
    plan_tasks = state.get("plan_tasks", [])
    if not plan_tasks:
        return state

    # Convert to list of dicts if needed
    tasks_list = []
    for task in plan_tasks:
        if hasattr(task, 'model_dump'):
            task_dict = task.model_dump()
        elif isinstance(task, dict):
            task_dict = task.copy()
        else:
            task_dict = {
                'id': getattr(task, 'id', ''),
                'order': getattr(task, 'order', 0),
                'action': getattr(task, 'action', ''),
                'agent': getattr(task, 'agent', ''),
                'tool': getattr(task, 'tool', ''),
                'why': getattr(task, 'why', ''),
                'status': getattr(task, 'status', 'pending'),
                'error': getattr(task, 'error', None)
            }
        tasks_list.append(task_dict)

    # Update the specific task
    for task in tasks_list:
        if task['id'] == task_id:
            task['status'] = new_status
            if error_message:
                task['error'] = error_message
            elif new_status == 'completed':
                task['error'] = None  # Clear any previous errors
            break

    # Convert back to Task objects and update state
    updated_state = state.copy()
    updated_state["plan_tasks"] = _convert_to_task_objects(tasks_list)

    return updated_state


##########################################################################
###################### BOND AI WORKFLOW BUILDER ##########################
##########################################################################

def create_workflow_from_registry():
    """Create workflow using the agent registry."""
    workflow = StateGraph(BondAIWorkflowState)
    
    workflow.add_node("table_indexing", table_indexing_node)
    workflow.add_edge("table_indexing", "supervisor")


    llm_agent = llm_no_thinking
    # Create ReAct agents from registry
    react_agents = supervisor_sub_agent_registry.get_react_agents()
    for name, config in react_agents.items():
        agent_params = {
            "name": name,
            "system_prompt": config["system_prompt"],
            "tools": config["tools"],
            "llm": llm_agent
        }
        
        # Check if agent has state injections
        if config.get("state_injections"):
            agent_params["state_injections"] = config["state_injections"]
            node = _create_react_agent(**agent_params)
        else:
            node = _create_react_agent(**agent_params)
        
        workflow.add_node(name, node)
        logging.info(f"✓ Added ReAct agent: {name}")

    # Add custom nodes from registry
    custom_nodes = supervisor_sub_agent_registry.get_custom_nodes()
    for name, config in custom_nodes.items():
        node_function = config["node_function"]
        workflow.add_node(name, node_function)
        logging.info(f"✓ Added custom node: {name}")

    # Add supervisor, error response, and task completion nodes
    workflow.add_node("supervisor", supervisor_agent_anthropic)
    workflow.add_node("error_response", error_response_node)
    workflow.add_node("task_completion", task_completion_node)

    # Connect all agents/nodes to supervisor
    current_members = supervisor_sub_agent_registry.get_agent_names()
    for member in current_members:
        workflow.add_edge(member, "supervisor")

    # Connect error response node to END
    workflow.add_edge("error_response", END)

    # Connect task completion node to supervisor
    workflow.add_edge("task_completion", "supervisor")

    # Create conditional routing
    conditional_map = {k: k for k in current_members}
    conditional_map["FINISH"] = END
    conditional_map["error_response"] = "error_response"
    conditional_map["task_completion"] = "task_completion"
    workflow.add_conditional_edges("supervisor", lambda x: x["next"], conditional_map)

    workflow.set_entry_point("table_indexing")
    return workflow.compile()

def create_async_workflow_from_registry():
    """Create async workflow using the agent registry."""
    workflow = StateGraph(BondAIWorkflowState)

    # Add async nodes
    workflow.add_node("table_indexing", table_indexing_node)  # Can be async too
    workflow.add_edge("table_indexing", "supervisor")


    
    llm_agent = llm_no_thinking
    # Create async ReAct agents
    react_agents = supervisor_sub_agent_registry.get_react_agents()
    for name, config in react_agents.items():
        agent_params = {
            "name": name,
            "system_prompt": config["system_prompt"],
            "tools": config["tools"],
            "llm": llm_agent
        }

        # Check if agent has state injections
        if config.get("state_injections"):
            agent_params["state_injections"] = config["state_injections"]
          
        node = _create_async_react_agent(**agent_params)

        workflow.add_node(name, node)
        logging.info(f"✓ Added async ReAct agent: {name}")

    # Add custom nodes from registry (with async handling for ICP agent)
    custom_nodes = supervisor_sub_agent_registry.get_custom_nodes()
    for name, config in custom_nodes.items():
        node_function = config["node_function"]

        # Use async version for ICP agent in async workflow
        if name == "icp_agent":
            workflow.add_node(name, icp_agent_wrapper)  # Use async version
        else:
            workflow.add_node(name, node_function)

        logging.info(f"✓ Added custom node: {name}")

    # Add async supervisor, error response, and task completion nodes
    workflow.add_node("supervisor", supervisor_agent_anthropic)  # Now async
    workflow.add_node("error_response", error_response_node)
    workflow.add_node("task_completion", task_completion_node)

    # Connect all agents/nodes to supervisor
    current_members = supervisor_sub_agent_registry.get_agent_names()
    for member in current_members:
        workflow.add_edge(member, "supervisor")

    # Connect error response node to END
    workflow.add_edge("error_response", END)

    # Connect task completion node to supervisor
    workflow.add_edge("task_completion", "supervisor")

    # Create conditional routing
    conditional_map = {k: k for k in current_members}
    conditional_map["FINISH"] = END
    conditional_map["error_response"] = "error_response"
    conditional_map["task_completion"] = "task_completion"
    workflow.add_conditional_edges("supervisor", lambda x: x["next"], conditional_map)

    workflow.set_entry_point("table_indexing")
    return workflow.compile()

# Create the initial workflow
graph = create_workflow_from_registry()

# Create async workflow instance
async_graph = create_async_workflow_from_registry()



##########################################################################
######################    UTILITY FOR PYTEST    ##########################
##########################################################################
def get_graph(async_mode: bool = False):
    """Get the appropriate graph based on async mode preference."""
    if async_mode:
        return async_graph
    return graph

# for pytest
def get_node_for_testing(node_name: str):
    """Get a node function that can be used for testing."""
    # Check if it's a ReAct agent
    react_agents = supervisor_sub_agent_registry.get_react_agents()
    if node_name in react_agents:
        config = react_agents[node_name]
        agent_params = {
            "name": node_name,
            "system_prompt": config["system_prompt"],
            "tools": config["tools"],
            "llm": llm_thinking
        }
        
        if config.get("state_injections"):
            agent_params["state_injections"] = config["state_injections"]
           
        return _create_react_agent(**agent_params)
    
    # Check if it's a custom node
    custom_nodes = supervisor_sub_agent_registry.get_custom_nodes()
    if node_name in custom_nodes:
        return custom_nodes[node_name]["node_function"]
    
    # Check built-in nodes
    if node_name == "planner":
        return planner_agent
    elif node_name == "table_indexing":
        return table_indexing_node
    elif node_name == "supervisor":
        return supervisor_agent_anthropic
    elif node_name == "error_response":
        return error_response_node
    elif node_name == "task_completion":
        return task_completion_node
    
    raise ValueError(f"Node '{node_name}' not found in registry")

