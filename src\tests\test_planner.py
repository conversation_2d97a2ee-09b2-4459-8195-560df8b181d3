import pytest
import sys
import os

# Add the project root to path
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, project_root)

from src.agents.bond_ai.nodes.planner_node import planner_agent
from src.agents.bond_ai.graph import get_node_for_testing
from langgraph.types import RunnableConfig
from langchain_core.messages import HumanMessage

def test_planer_node():
    """Test the planner node."""
    table_summary = """{
        "column_name": "Full Name",
        "column_id": 1,
        "data_summary": "This column contains the full names of individuals, indicating a diverse range of individuals with various backgrounds. Notably, some entries include professional designations, like 'CFA,' hinting at financial or investment-related professions.",
        "is_runnable": false,
        "Full Name": {
            "cell_value": "..."
        }
        }

        {
        "column_name": "Company Name",
        "column_id": 3,
        "data_summary": "All entries in this column refer to 'Algebris Investments,' suggesting that the data represents individuals associated with a single organization. This could be useful for identifying the team's structure and key personnel within the company.",
        "is_runnable": false,
        "Company Name": {
            "cell_value": "..."
        }
        }

        {
        "column_name": "Job Title",
        "column_id": 2,
        "data_summary": "The job titles encompass a variety of roles within the organization, primarily focused on business development and portfolio management. This indicates a concentration of expertise in investment operations and strategic development, relevant for business growth.",
        "is_runnable": false,
        "Job Title": {
            "cell_value": "..."
        }
        }

        {
        "column_name": "LinkedIn Profile",
        "column_id": 4,
        "data_summary": "This column lists unique LinkedIn profile URLs for each individual, serving as digital identifiers that can connect to their professional profiles. This can be particularly useful for networking, recruitment, and gathering further information regarding each individual's professional background.",
        "is_runnable": false,
        "LinkedIn Profile": {
            "cell_value": "..."
        }
        }"""
    state = {
        "table_summary": table_summary,
        "messages": [
            HumanMessage(content="Find 50 marketing managers at SaaS companies in San Francisco, enrich them and create personalized outreach messages.")
        ]
    }
    
    # Create a test config
    test_config = RunnableConfig(
        configurable={
            "table_id": "tbl_test",
            "model": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0"
        }
    )
    
    # Test the planner node directly
    result = planner_agent(state, test_config)
    print(result)
    
    assert "plan_tasks" in result or "messages" in result
    
    # Display the plan_tasks result
    if "plan_tasks" in result:
        print("\n=== PLAN TASKS ===")
        for i, task in enumerate(result["plan_tasks"], 1):
            print(f"\nTask {i}:")
            print(f"  ID: {task.id}")
            print(f"  Order: {task.order}")
            print(f"  Action: {task.action}")
            print(f"  Agent: {task.agent}")
            print(f"  Tool: {task.tool}")
            print(f"  Why: {task.why}")
            print(f"  Status: {task.status}")
            if task.error:
                print(f"  Error: {task.error}")
        print(f"\nTotal tasks: {len(result['plan_tasks'])}")
    else:
        print("\nNo plan_tasks found in result")
        
    assert len(result["plan_tasks"]) > 0

def test_planer_node_via_registry():
    """Test the planner node via the registry system."""
    # Get the node for testing
    planner_node = get_node_for_testing("planner")
    
    # Same test state as above
    state = {
        "table_summary": "...",  # abbreviated for brevity
        "messages": [
            HumanMessage(content="Find 50 marketing managers at SaaS companies in San Francisco, enrich them and create personalized outreach messages.")
        ]
    }
    
    # Create a test config
    test_config = RunnableConfig(
        configurable={
            "table_id": "tbl_test",
            "model": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0"
        }
    )
    
    result = planner_node(state, test_config)
    assert "plan_tasks" in result or "messages" in result
