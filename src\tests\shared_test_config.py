"""
Shared test configuration and data for reuse across test files.
"""

from langgraph.types import RunnableConfig

# Shared test configuration
TEST_CONFIG = RunnableConfig(
    configurable={
        "table_id": "tbl_c19353f2f59e4786",
        "model": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0"
    }
)

# Shared table summary for testing
TABLE_SUMMARY = """{
    "column_name": "Full Name",
    "column_id": 1,
    "data_summary": "This column contains the full names of individuals, indicating a diverse range of individuals with various backgrounds. Notably, some entries include professional designations, like 'CFA,' hinting at financial or investment-related professions.",
    "is_runnable": false,
    "Full Name": {
        "cell_value": "..."
    }
    }

    {
    "column_name": "Company Name",
    "column_id": 3,
    "data_summary": "All entries in this column refer to 'Algebris Investments,' suggesting that the data represents individuals associated with a single organization. This could be useful for identifying the team's structure and key personnel within the company.",
    "is_runnable": false,
    "Company Name": {
        "cell_value": "..."
    }
    }

    {
    "column_name": "Job Title",
    "column_id": 2,
    "data_summary": "The job titles encompass a variety of roles within the organization, primarily focused on business development and portfolio management. This indicates a concentration of expertise in investment operations and strategic development, relevant for business growth.",
    "is_runnable": false,
    "Job Title": {
        "cell_value": "..."
    }
    }

    {
    "column_name": "LinkedIn Profile",
    "column_id": 4,
    "data_summary": "This column lists unique LinkedIn profile URLs for each individual, serving as digital identifiers that can connect to their professional profiles. This can be particularly useful for networking, recruitment, and gathering further information regarding each individual's professional background.",
    "is_runnable": false,
    "LinkedIn Profile": {
        "cell_value": "..."
    }
    }"""
