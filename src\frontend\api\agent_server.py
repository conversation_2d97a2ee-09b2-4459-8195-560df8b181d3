import httpx
import os
import json
import uuid
from uuid import UUID
from dotenv import load_dotenv

from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import traceback


load_dotenv()


LANGGRAPH_SERVER_URL = os.getenv("LANGGRAPH_SERVER_URL", "http://localhost:2024")
if not LANGGRAPH_SERVER_URL:
    raise ValueError("LANGGRAPH_SERVER_URL environment variable not found. Please set it in your .env file or environment.")
CATCHUP_API_KEY = os.getenv('CATCHUP_API_KEY', "")
if CATCHUP_API_KEY:
    headers = {"x-api-key": CATCHUP_API_KEY}
else:
    headers = {}



# ----------------------------
# Thread Management
# ----------------------------


async def create_thread(user_id: UUID) -> UUID:
    """Create a new thread for the given user."""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                url=f"{LANGGRAPH_SERVER_URL}/threads",
                headers=headers,
                json={
                    "thread_id": str(uuid.uuid4()),
                    "metadata": {
                        "user_id": str(user_id)
                    },
                    "if_exists": "do_nothing" # returns existing thread
                },
                timeout=120.0 # Added timeout to wait for Render spin up
            )
            response.raise_for_status()
            thread_id = response.json().get("thread_id")

            return UUID(thread_id)
    except Exception as e:
        print(f"Request failed: {e}")
        raise


async def search_threads(user_id: UUID) -> List[UUID]:
    """Create a new thread for the given user."""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                url=f"{LANGGRAPH_SERVER_URL}/threads/search",
                headers=headers,
                json={
                    "metadata": {
                        "user_id": str(user_id)
                    },
                },
                timeout=120.0 # Added timeout to wait for Render spin up
            )
            response.raise_for_status()

            return [UUID(thread["thread_id"]) for thread in response.json()]
    except Exception as e:
        print(f"Request failed: {e}")
        raise


async def delete_thread(thread_id: UUID) -> None:
    """Delete a thread by its ID."""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.delete(
                url=f"{LANGGRAPH_SERVER_URL}/threads/{thread_id}",
                headers=headers,
                timeout=120.0 # Added timeout to wait for Render spin up
            )
            response.raise_for_status()

            # DELETE requests might not return JSON content
            if response.status_code == 200:
                return None
    except Exception as e:
        print(f"Request failed: {e}")
        raise


# ----------------------------
# Thread Runs
# ----------------------------


class Event(BaseModel):
    event_type: str
    data: Dict[str, Any]


class SSEParser:
    """Proper SSE (Server-Sent Events) parser that handles event boundaries correctly."""

    def __init__(self):
        self.buffer = ""
        self.current_tool_call = None
        self.seen_message_ids = set()  # Track processed messages to avoid duplicates

    def parse_chunk(self, chunk: bytes) -> List[str]:
        """Parse a chunk of SSE data and return complete events."""
        # Decode and add to buffer
        chunk_str = chunk.decode('utf-8')
        self.buffer += chunk_str

        results = []

        # Split on double CRLF to separate events (SSE uses \r\n\r\n)
        while '\r\n\r\n' in self.buffer:
            event_data, self.buffer = self.buffer.split('\r\n\r\n', 1)

            # Parse the event
            result = self._parse_event(event_data)
            if result:
                results.append(result)

        return results

    def _parse_event(self, event_data: str) -> str:
        """Parse a single SSE event."""
        # print(f"[DEBUG] Parsing event data: {repr(event_data)}")
        
        lines = event_data.strip().split('\n')
        event_type = None
        data = None

        for line in lines:
            line = line.strip()
            if line.startswith('event:'):
                event_type = line[6:].strip()
            elif line.startswith('data:'):
                data = line[5:].strip()

        # print(f"[DEBUG] Extracted - event_type: {event_type}, data: {data}")
        
        if not event_type or not data:
            return ""

        return self._process_event(event_type, data)

    def _process_event(self, event_type: str, data: str) -> str:
        """Process a parsed SSE event."""
        if event_type == 'error':
            return f"\n\n```\nERROR: {data}\n```\n\n"

        elif event_type == 'metadata':
            # Just log metadata, don't return anything
            return ""

        elif event_type == 'messages':
            try:
                # For messages-tuple mode, data is a tuple [node_name, message]
                parsed_data = json.loads(data)

                # Handle both tuple format and direct message format
                if isinstance(parsed_data, list) and len(parsed_data) >= 1:
                    message = parsed_data[0]  # Get first element (the actual message)
                    if isinstance(message, dict):
                        return self._process_message(message)

                return ""

            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                print(f"Data: {data}")
                return ""

        return ""

    def _process_message(self, message: Dict[str, Any]) -> str:
        """Process a message from the stream."""
        message_type = message.get("type")

        # Handle AI messages (complete messages, not chunks)
        if message_type == "ai":
            content = message.get("content", "")
            if content:
                return content
            return ""

        # Handle streaming AI message chunks for real-time output
        elif message_type == "AIMessageChunk":
            return self._process_ai_message_chunk(message)

        # Only deduplicate tool messages, not AI message chunks
        elif message_type == "tool":
            message_id = message.get("id")
            if message_id and message_id in self.seen_message_ids:
                return ""  # Skip duplicate tool messages

            # Mark this tool message as seen
            if message_id:
                self.seen_message_ids.add(message_id)

            return self._process_tool_message(message)

        return ""

    def _process_ai_message_chunk(self, message: Dict[str, Any]) -> str:
        """Process an AI message chunk for streaming."""
        result = ""

        # Handle regular content streaming
        if message.get("content"):
            result = message["content"]

        # Handle tool calls
        elif message.get("tool_calls"):
            tool_call = message["tool_calls"][0]
            if tool_call.get("name") and tool_call.get("id"):
                # Start of a new tool call
                self.current_tool_call = {
                    "name": tool_call["name"],
                    "id": tool_call["id"],
                    "args": ""
                }
                result = f"\n🔧 **Tool Call: {tool_call['name']}**\n\n"

        # Handle tool call chunks (streaming arguments)
        elif message.get("tool_call_chunks"):
            chunk_data = message["tool_call_chunks"][0]
            if self.current_tool_call and chunk_data.get("args"):
                self.current_tool_call["args"] += chunk_data["args"]

        return result

    def _process_tool_message(self, message: Dict[str, Any]) -> str:
        """Process a tool response message."""
        tool_name = message.get("name", "Unknown")
        # tool_response = message.get("content", "No response") # optionally can include tool output

        return f"✅ **Tool Response: {tool_name}**\n\n"


async def run_stream_from_message(
    thread_id: UUID,
    assistant_id: str,
    message: str,
    configurable: dict,
    parser: Optional[SSEParser] = None,
    user_id: Optional[str] = None,
    email_address: Optional[str] = None,
    latitude: Optional[str] = None,
    longitude: Optional[str] = None,
    session_id: Optional[str] = None,
    memory_lenght: Optional[str] = None
):
    """Stream messages from the langgraph API"""
    if parser is None:
        parser = SSEParser()

    # Build the input payload
    input_payload = {
        "messages": [message]
    }

    # Add optional parameters if provided
    if user_id is not None:
        input_payload["user_id"] = user_id
    if email_address is not None:
        input_payload["email_address"] = email_address
    if latitude is not None:
        input_payload["latitude"] = latitude
    if longitude is not None:
        input_payload["longitude"] = longitude
    if session_id is not None:
        input_payload["session_id"] = session_id
    if memory_lenght is not None:
        input_payload["memory_lenght"] = memory_lenght

    # print(f"[DEBUG] Making request to: {LANGGRAPH_SERVER_URL}/threads/{str(thread_id)}/runs/stream")
    # print(f"[DEBUG] Payload: {json.dumps(input_payload, indent=2)}")

    try:
        with httpx.stream(
            method="POST",
            headers=headers,
            url=f"{LANGGRAPH_SERVER_URL}/threads/{str(thread_id)}/runs/stream",
            json={
                "assistant_id": assistant_id,
                "input": input_payload,
                "config": {
                    "recursion_limit": 50,
                    "configurable": configurable
                    },
                "stream_mode": "messages-tuple",  # Change back to tuple mode
                "stream_subgraphs": False,
            },
            timeout=120.0
            ) as stream:
            
            # print(f"[DEBUG] Stream response status: {stream.status_code}")

            for chunk in stream.iter_bytes():
                if not chunk:
                    continue
                
                # print(f"[DEBUG] Raw chunk: {chunk}")  # Show the actual raw data
                
                try:
                    # Parse the chunk and get any complete events
                    results = parser.parse_chunk(chunk)
                    # print(f"[DEBUG] Parser returned {len(results)} results: {results}")
                    
                    for result in results:
                        if result:
                            # print(f"[DEBUG] Yielding non-empty result: {repr(result)}")
                            yield result
                        # else:
                            # print(f"[DEBUG] Skipping empty result")

                except Exception as e:
                    print(f"Error processing chunk: {str(e)}")
                    print(f"Chunk content: {chunk}")
                    continue

    except Exception as e:
        print(f"Error in run_stream_from_message: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        
