"""
Test for LIMA data provider API endpoint.

This test directly invokes the LIMA API endpoint to verify connectivity and response format.
"""

import os
from dotenv import load_dotenv
import requests
import json
from typing import Dict, Any

load_dotenv()

def test_lima_api_endpoint():
    """
    Simple test to invoke the LIMA API endpoint and verify response.
    
    This test makes a direct POST request to the LIMA API endpoint
    with a basic filter to test connectivity and response format.
    """
    
    # Get API key from environment
    LIMA_API_KEY = os.getenv("LIMA_API_KEY")
    
    if not LIMA_API_KEY:
        print("❌ Error: LIMA_API_KEY environment variable is not set.")
        return False
    
    print("🔑 LIMA API Key found")
    
    # Prepare headers
    headers = {
        'Content-Type': 'application/json',
        'X-Api-Key': f'{LIMA_API_KEY}'
    }
    
    
    url = "https://api.limadata.com/api/v2/prospect/live/people/filter"
    payload = {
    "filters": [
        {
            "filter_type": "company",
            "operator": "in",
            "values": ["https://linkedin.com/company/microsoft", "https://linkedin.com/company/google"]
        },
        {
            "filter_type": "current_title",
            "operator": "in",
            "values": ["Vice President Marketing", "Head of Marketing"]
        },
        {
            "filter_type": "location",
            "operator": "in",
            "values": ["New York City Metropolitan Area", "San Francisco Bay Area"]
        }
    ],
    "page": 1
    }
    payload = {
        "filters": [
            {
                "filter_type": "company", 
                "operator": "in", 
                "values": ["outbond"]
            }
    ], "page": 1}
    
    
    print("📤 Sending request to LIMA API...")
    print(f"Endpoint: https://api.limadata.com/api/v2/prospect/live/people/filter")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        # Make the API request
        response = requests.post(url, json=payload, headers=headers)
        
        print(f"📥 Response Status Code: {response.status_code}")
        print(response.json())
        # Check response status
        if response.status_code == 200:
            print("✅ API request successful!")
            
            try:
                data = response.json()
                print(f"📊 Response data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                
                # Check if we have profiles in the response
                if isinstance(data, dict) and 'profiles' in data:
                    profiles_count = len(data['profiles'])
                    print(f"👥 Found {profiles_count} profiles")
                    
                    if profiles_count > 0:
                        print("📋 Sample profile structure:")
                        sample_profile = data['profiles'][0]
                        for key, value in sample_profile.items():
                            print(f"  - {key}: {value}")
                
                # Check for total count
                if isinstance(data, dict) and 'total_display_count' in data:
                    total_count = data['total_display_count']
                    print(f"🔢 Total available profiles: {total_count}")
                
                return True
                
            except json.JSONDecodeError:
                print("⚠️  Response is not valid JSON")
                print(f"Raw response: {response.text[:500]}...")
                return False
                
        elif response.status_code == 401:
            print("❌ Authentication failed. Please check your LIMA_API_KEY.")
            return False
            
        elif response.status_code == 400:
            print("❌ Bad request. Check the payload format.")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Raw error response: {response.text}")
            return False
            
        elif response.status_code == 402:
            print("💳 Payment required. Check your LIMA account billing status.")
            return False
            
        elif response.status_code == 429:
            print("⏰ Rate limit exceeded. Please try again later.")
            return False
            
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(f"Response: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out. Please try again.")
        return False
        
    except requests.exceptions.ConnectionError:
        print("🌐 Connection error. Please check your internet connection.")
        return False
        
    except Exception as e:
        print(f"❌ An error occurred: {str(e)}")
        return False


def test_lima_api_with_different_filters():
    """
    Test LIMA API with different filter combinations to verify flexibility.
    """
    
    LIMA_API_KEY = os.getenv("LIMA_API_KEY")
    
    if not LIMA_API_KEY:
        print("❌ Error: LIMA_API_KEY environment variable is not set.")
        return False
    
    headers = {
        'Content-Type': 'application/json',
        'X-Api-Key': f'{LIMA_API_KEY}'
    }
    
    # Test different filter combinations
    test_cases = [
        {
            "name": "Test 1: Current Title Filter",
            "payload": {
                "filters": [
                    {
                        "filter_type": "CURRENT_TITLE",
                        "type": "in",
                        "value": ["Software Engineer"]
                    }
                ],
                "page": 1
            }
        },
        {
            "name": "Test 2: Function Filter",
            "payload": {
                "filters": [
                    {
                        "filter_type": "FUNCTION",
                        "type": "in",
                        "value": ["Engineering"]
                    }
                ],
                "page": 1
            }
        },
        {
            "name": "Test 3: Company Headcount Filter",
            "payload": {
                "filters": [
                    {
                        "filter_type": "COMPANY_HEADCOUNT",
                        "type": "in",
                        "value": ["1,001-5,000"]
                    }
                ],
                "page": 1
            }
        }
    ]
    
    success_count = 0
    
    for test_case in test_cases:
        print(f"\n🧪 {test_case['name']}")
        print(f"Payload: {json.dumps(test_case['payload'], indent=2)}")
        
        try:
            response = requests.post(
                'https://api.limadata.com/api/v2/prospect/live/people/filter',
                headers=headers,
                json=test_case['payload'],
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ Success")
                success_count += 1
            else:
                print(f"❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    print(f"\n📊 Summary: {success_count}/{len(test_cases)} tests passed")
    return success_count == len(test_cases)


if __name__ == "__main__":
    print("🚀 Starting LIMA API Tests")
    print("=" * 50)
    
    # Run basic test
    print("\n🔍 Running basic API test...")
    basic_test_result = test_lima_api_endpoint()
    
    # # Run filter tests
    # print("\n🔍 Running filter variation tests...")
    # filter_test_result = test_lima_api_with_different_filters()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"Basic API Test: {'✅ PASSED' if basic_test_result else '❌ FAILED'}")
  #  print(f"Filter Tests: {'✅ PASSED' if filter_test_result else '❌ FAILED'}")
    
    # if basic_test_result and filter_test_result:
    #     print("\n🎉 All tests passed! LIMA API is working correctly.")
    # else:
    #     print("\n⚠️  Some tests failed. Please check the output above for details.")
