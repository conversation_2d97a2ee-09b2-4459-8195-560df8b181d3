"""LinkedIn profiles search tool."""

import json
from typing import List, Union, Dict, Any, Tuple, Optional, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg,InjectedToolCallId
from langgraph.types import Command
from langchain_core.messages import ToolMessage
from langgraph.prebuilt import InjectedState
from langgraph.config import get_stream_writer
from bond_ai.state import BondAIWorkflowState
from bond_ai.configuration import Configuration
import os
import requests

from ...models.linkedin_filters import LinkedinSearchFilterTypeEnum as LinkedinSearchFilterType, PersonSearchRequest,create_filter
from ...agent_db import get_table_row_count, create_table_row, save_linkedin_profiles_to_separate_columns, create_linkedin_profile_columns

#CRUSTDATA_API_KEY = os.getenv("CRUSTDATA_API_KEY")
LIMA_API_KEY = os.getenv("LIMA_API_KEY")

    
@tool
def search_linkedin_profiles(
    tool_call_id: Annotated[str, InjectedToolCallId],
    state: Annotated[BondAIWorkflowState, InjectedState],
    config: Annotated[RunnableConfig, InjectedToolArg],
    filters: List[Union[LinkedinSearchFilterType, Dict[str, Any]]],
    page: int = 1,
) -> Command: #Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Search for LinkedIn profiles using LimaData API and save results to table.
    This tool searches for LinkedIn profiles based on specified filters and automatically
    saves the results to the current table in separate columns (Full Name, Job Title, 
    Company Name, LinkedIn URL).
    Parameters:
        config: Configuration injected by the system
        filters: List of search filters to apply. 
                 Each filter should specify the filter_type, operators, and values to filter by.
                 operator can have only one of the following 2 values: 'in', 'not in'
                 Example:   
                 [{'filter_type': 'company_type', 'operator': 'in', 'values': ['Technology']}, {'filter_type': 'company_headquarters', 'operator': 'in', 'values': ['United States','Canada','United Kingdom']}, {'filter_type': 'company_headcount', 'operator': 'lt', 'values': ['100']}]  
                 
        page: Page number for pagination (default: 1)
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (search_results, error)
        where search_results contains information about the search and saved profiles,
        None if failed, and error is the error message if failed, None if successful
    """
    try:
        if not LIMA_API_KEY:
            return None, "Error: LIMA_API_KEY environment variable is not set."
        print("[DEBUG] Filters input", filters)
        
        # Get configuration
        configuration = Configuration.from_runnable_config(config)
   
        table_id = configuration.table_id
        
       # Process filters to ensure they're in the correct format
        processed_filters = []
        for filter_item in filters:
            if isinstance(filter_item, dict):
                # Convert dictionary to proper filter object
                try:
                    filter_type = filter_item.get('filter_type')
                    if not filter_type:
                        return None, f"Missing 'filter_type' in filter: {filter_item}"
                    
                    # Extract other parameters
                    filter_params = {k: v for k, v in filter_item.items() if k != 'filter_type'}
                    
                    # Create the proper filter object
                    filter_obj = create_filter(filter_type, **filter_params)
                    processed_filters.append(filter_obj)
                except Exception as e:
                    return None, f"Error converting filter {filter_item}: {str(e)}"
            else:
                # Already a proper filter object
                processed_filters.append(filter_item)
        
        # Create or get the LinkedIn profile columns
        column_ids, column_error = create_linkedin_profile_columns(table_id)
        if column_error:
            return None, f"Error creating LinkedIn profile columns: {column_error}"
        
        # Ensure table has at least 25 rows for the search results
        row_count, row_count_error = get_table_row_count(table_id)
        if row_count_error:
            return None, f"Error checking table row count: {row_count_error}"
        
        # If table has fewer than 25 rows, create the needed rows
        if row_count < 25:
            rows_to_create = 25 - row_count
            print(f"Table has {row_count} rows, creating {rows_to_create} additional rows to reach 25")
            
            for i in range(rows_to_create):
                # Index should be current row_count + i + 1 (1-based indexing)
                index = row_count + i + 1
                create_row_result, create_row_error = create_table_row(table_id, index)
                if create_row_error:
                    return None, f"Error creating row {index}: {create_row_error}"
            
            print(f"Successfully created {rows_to_create} rows")
        else:
            print(f"Table already has {row_count} rows (>= 25), no additional rows needed")
        
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": "Searching LinkedIn Profiles"})
        #print("[Debug] Search LinkedIn Profiles",processed_filters)
        # Create the request model with processed filters and page
        try:
            search_request = PersonSearchRequest(filters=processed_filters, page=page)
        except Exception as e:
            msg = f"Error creating search request: {str(e)}"
            return Command( update = {
                "last_error_message": msg,
                "messages":[ToolMessage(
                    content=msg,  
                    tool_call_id=tool_call_id,
                )],
                    
                
            } )
        
        #print("[Debug] Search LinkedIn Profiles",search_request)    
        
        # Convert to dictionary for the API call
        payload = search_request.model_dump(mode='json')
        
      
        print("[Debug] Payload", payload )
        # Make the API request
        headers = {
            'Content-Type': 'application/json',
            #'Accept': 'application/json, text/plain, */*',
            #'Authorization': f'Token {LIMA_API_KEY}'
            'X-Api-Key': f'{LIMA_API_KEY}'
        }
        
        
        url = "https://api.limadata.com/api/v2/prospect/live/people/filter"
        response = requests.post(url,headers=headers,json=payload)
        #print("[Debug] Response api", response.json() )
        
        if response.status_code != 200:
            msg = {
                400:response.json() ,
                500: "Internal server error. Please try again later."
                
            }.get(response.status_code, f"API request failed with status {response.status_code}: {response.text}")
            return Command( update = {
                "last_error_message": response.status_code,
                "messages":[ToolMessage(
                    content=msg,  
                    tool_call_id=tool_call_id,
                )],
                    
                
            }   )
        
        # Check response status
        
        data = response.json()
            
            # Save profiles to separate columns if we have profiles in the response
        profiles_saved_count = 0
        if 'people' in data and data['people']:
            save_success, save_error = save_linkedin_profiles_to_separate_columns(table_id, data['people'], column_ids)
            if save_error:
                print(f"Warning: Failed to save profiles to columns: {save_error}")
            else:
                profiles_saved_count = len(data['people'])
                print(f"Successfully saved {profiles_saved_count} profiles to separate columns")
            
        # Construct informative response for the agent (without returning actual profiles)
        total_display_count = data.get('total_display_count', 0)
            
        response_data = {
                "message": f"LinkedIn search completed successfully. Found {total_display_count} total profiles matching your criteria. Saved {profiles_saved_count} profiles to the table across 4 columns (Full Name, Job Title, Company Name, LinkedIn URL).",
                "total_display_count": total_display_count,
                "profiles_saved_to_table": profiles_saved_count,
                "columns_created": list(column_ids.keys()),
                "column_ids": column_ids
            }
            
            
        return Command ( update = {
                "last_error_message": None,
                "messages":[ToolMessage(
                    content=json.dumps(response_data),  
                    tool_call_id=tool_call_id,
                )],
                    
                
            })
            #return response_data, None
       
       # elif response.status_code == 500:
            # msg = {
            #     400:response.json()  
                
                
            # }
        #    return None, "Internal server error. Please try again later."
        # elif response.status_code == 401:
        #     return None, "Authentication failed. Please check your CRUSTDATA_API_KEY."
        # elif response.status_code == 400:
        #     return None, response.json()
        # elif response.status_code == 429:
        #     return None, "Rate limit exceeded. Please try again later."
        # else:
          
        #     return None, f"API request failed with status {response.status_code}: {response.text}"

    except Exception as e:
        return Command( update = {
                "last_error_message": f"An error occurred last while searching LinkedIn profiles : {str(e)}",
                "messages":[ToolMessage(
                    content=f"An error  while message searching LinkedIn profiles : {str(e)}",  
                    tool_call_id=tool_call_id,
                )],
                    
                
            }   )            
    # except requests.exceptions.Timeout:
    #     return None, "Request timed out. Please try again."
    # except requests.exceptions.ConnectionError:
    #     return None, "Connection error. Please check your internet connection."
    # except Exception as e:
    #     return None, f"An error occurred while searching LinkedIn profiles: {str(e)}"
