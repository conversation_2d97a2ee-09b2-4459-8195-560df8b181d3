"""
Central configuration module using Pydantic for environment variables.

This module provides a single source of truth for all environment variables
across the application, with type validation and default values.
"""

from functools import lru_cache
import logging
from typing import Optional
from pydantic import Field, SecretStr, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv
import os

logger = logging.getLogger("backend_api.config")

# Load environment variables at the start
load_dotenv()



class Settings(BaseSettings):
    """Application settings loaded from environment variables with validation."""

    model_config = SettingsConfigDict(
        case_sensitive=True, env_file=".env", env_file_encoding="UTF-8", extra="ignore"
    )
    # API and server settings
    ENV: str = Field(
        "development", description="Environment (development, staging, production)"
    )
    DEBUG: bool = Field(False, description="Debug mode")

    # Application URL
    APP_URL: str = Field(
        "http://localhost:5173", description="Application frontend URL"
    )

    # Langsmith
    LANGSMITH_PROJECT: str = Field("outboud-ai", description="Langsmith project name")
    LANGSMITH_API_KEY: SecretStr = Field(..., description="Langsmith API key")

    # Supabase settings
    SUPABASE_URL: str = Field(..., description="Supabase URL")
    SUPABASE_KEY: SecretStr = Field(..., description="Supabase anonymous key")
    SUPABASE_SERVICE_ROLE_KEY: SecretStr = Field(
        ..., description="Supabase service role key"
    )

    # Redis settings for Celery
    # REDIS_URL: SecretStr = Field(None, description="Redis URL for Celery")

    CELERY_BROKER_URL: str = Field(..., description="Celery broker URL")
    CELERY_RESULT_BACKEND: str = Field(..., description="Celery result backend")

    POSTGRES_URL: SecretStr = Field(..., description="PostgreSQL URL")

    # LLM Choices
    OPENAI_API_KEY: Optional[SecretStr] = Field(..., description="OpenAI API key")
    ANTHROPIC_API_KEY: Optional[SecretStr] = Field(..., description="Anthropic API key")
    FIREWORKS_API_KEY: Optional[SecretStr] = Field(..., description="Fireworks API key")
    PROXYCURL_API_KEY: SecretStr = Field(..., description="ProxyCurl API key")
    LEADMAGIC_API_KEY: SecretStr = Field(..., description="Leadmagic API key")
    PROSPEO_API_KEY: SecretStr = Field(..., description="Prospeo API key")
    FINDYMAIL_API_KEY: SecretStr = Field(..., description="Findymail API key")
    MILLIONVERIFIER_API_KEY: SecretStr = Field(
        ..., description="Millionverifier API key"
    )

    LIMA_API_KEY: SecretStr = Field(..., description="Lima API key")

    # LLM models
    RUN_ONLY_IF_MODEL: str = Field(
        "Bedrock/us.amazon.nova-micro-v1:0",
        description="Model for run_only_if evaluations",
    )
    AI_COLUMN_MODEL: str = Field(
        "openai/gpt-4o-mini", description="Model for AI column generation"
    )
    AI_FORMULA_MODEL: str = Field(
        "Bedrock/us.amazon.nova-micro-v1:0",
        description="Model for AI formula generation",
    )
    AI_RESEARCH_MODEL: str = Field(
        "Bedrock/us.amazon.nova-micro-v1:0",
        description="Model for AI research generation",
    )

    # Tools API keys
    TAVILY_API_KEY: SecretStr = Field(..., description="Tavily API key")
    FIRECRAWL_API_KEY: SecretStr = Field(..., description="Firecrawl API key")

    # AWS credentials
    AWS_ACCESS_KEY_ID: SecretStr = Field(..., description="AWS Access Key ID")
    AWS_SECRET_ACCESS_KEY: SecretStr = Field(..., description="AWS Secret Access Key")

    IS_STRESS_TESTING: bool = Field(
        False, description="Whether to run in stress testing mode"
    )

    TESTING_LINKEDIN_PROFILE_PICTURE_URL: str = Field(
        ..., description="LinkedIn profile picture URL"
    )

    @field_validator("SUPABASE_URL")
    @classmethod
    def validate_supabase_url(cls, v):
        """Ensure Supabase URL is properly formatted."""
        if v is not None and not v.startswith(("http://", "https://")):
            raise ValueError("SUPABASE_URL must start with http:// or https://")
        return v


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings from environment variables.

    Uses LRU cache to avoid loading .env file multiple times.

    Returns:
        Settings: Application settings
    """
    return Settings()
