"""LinkedIn profiles search tool."""

from dataclasses import dataclass
from typing import List, Union, Dict, Any, Tuple, Optional, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg,InjectedToolCallId
from langgraph.config import get_stream_writer
from bond_ai.state import BondAIWorkflowState
from bond_ai.utilities.prettify import pretty_print_messages
from bond_ai.configuration import Configuration
from dotenv import load_dotenv
from bond_ai.filter_models import FilterType as LinkedInFilterType
from langgraph.prebuilt import create_react_agent
load_dotenv()
from langchain_openai import ChatOpenAI

@dataclass(kw_only=True)
class Configuration:
    """The configuration for the agent."""

    #table_id: str = "tbl_0d5faf0901046890"
    table_id:str ="tbl_c19353f2f59e4786"
    model: str = "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0"




@tool
def add(a: float, b: float):
    """Add two numbers."""
    return a + b

@tool
def multiply(a: float, b: float):
    """Multiply two numbers."""
    return a * b

@tool
def divide(a: float, b: float):
    """Divide two numbers."""
    return a / b


math_agent = create_react_agent(
    model="openai:gpt-4.1",
    tools=[add, multiply, divide],
    prompt=(
        "You are a math agent.\n\n"
        "INSTRUCTIONS:\n"
        "- Assist ONLY with math-related tasks\n"
        "- After you're done with your tasks, respond to the supervisor directly\n"
        "- Respond ONLY with the results of your work, do NOT include ANY other text."
    ),
    name="math_agent",
    stream_mode="messages",  # Enable streaming
)
from langgraph.types import Command
from langchain_core.messages import ToolMessage
from langgraph.prebuilt import InjectedState
@tool
def search_linkedin_profiles(
    filters: Optional[List[Union[LinkedInFilterType, Dict[str, Any]]]] = None,
    search_query: Optional[str] = None,
    page: int = 1,
) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Search for LinkedIn profiles using advanced filters and create separate columns for profile data.
    
    This tool searches for people on LinkedIn based on various criteria such as 
    current company, job title, location, industry, seniority, and more.
    It creates separate columns for Full Name, Job Title, Company Name, and LinkedIn URL,
    then saves the profile data to these respective columns.
    USE THIS TOOL TO SEARCH FOR PROFILES TO BUILD USER'S PROSPECT LIST AND SAVE THEM TO THE TABLE.
    
    Parameters:
        config: Configuration injected by the system
        filters: List of filters to apply. Each filter should use the LinkedInFilterType models
                 or raw dictionaries that will be converted to the proper filter types.
                 All filters are combined with AND logic.
                 Example filters:
                 - CurrentCompanyFilter(type="in", value=["Google", "Microsoft"])
                 - CurrentTitleFilter(type="not in", value=["Software Engineer"])
                 - RegionFilter(type="in", value=["California, United States"])
        page: Page number for pagination (default: 1)
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (results, error)
        where results contains the search summary if successful, None if failed
        and error is the error message if failed, None if successful
        
    Note: Some filter values may need to be obtained from the Filters Autocomplete API
    for fields like COMPANY_HEADQUARTERS, REGION, and INDUSTRY to ensure valid values.
    """
    
    print("[DEBUG] TOOL CALLED - search_linkedin_profiles")
    print("[DEBUG] filters:", filters)
    print("[DEBUG] search_query:", search_query)
   
    
    # If no filters provided but search_query exists, create default CEO filters
    # if not filters and search_query:
    #     filters = [
    #         {"field": "job_title", "operator": "contains", "value": "CEO"},
    #         {"field": "location", "operator": "contains", "value": "United States"}
    #     ]
    # elif not filters:
    #     # Default filters for CEO search
    #     filters = [
    #         {"field": "job_title", "operator": "contains", "value": "CEO"},
    #         {"field": "location", "operator": "contains", "value": "United States"}
    #     ]
    
    # Mock response in tuple format: (search_results, error)
    search_results = {
        "message": "LinkedIn company search completed successfully. Found company profile for Outbond.io.",
        "search_type": "company",
        "company_profile": {
            "company_name": "Outbond.io",
            "industry": "Software Development",
            "company_size": "11-50 employees",
            "headquarters": "San Francisco, CA",
            "founded": "2020",
            "description": "AI-powered sales automation and lead generation platform",
            "linkedin_url": "https://linkedin.com/company/outbond-io",
            "website": "https://outbond.io",
            "specialties": ["Sales Automation", "AI", "Lead Generation", "B2B Sales"]
        }
    }
    error = None  # None indicates success
    mock_response = (search_results, error)
    total_display_count = 1
    profiles_saved_count = 1
    column_names = ["Company Name", "Industry", "Company Size", "LinkedIn URL"]
    column_ids = {name: f"col_{str(i+1).zfill(3)}" for i, name in enumerate(column_names)}
    
    # Update state (this happens automatically with injected state)
    try:
        response_data = {
            "message": f"LinkedIn search completed successfully. Found {total_display_count} total profiles matching your criteria. Saved {profiles_saved_count} profiles to the table across 4 columns (Full Name, Job Title, Company Name, LinkedIn URL).",
            "total_display_count": total_display_count,
            "profiles_saved_to_table": profiles_saved_count,
            "columns_created": list(column_ids.keys()),
            "column_ids": column_ids
        }
        
        return response_data, None
        
    except Exception as e:
        print(f"[ERROR] search_linkedin_profiles failed: {str(e)}")
        return None, f"LinkedIn search failed: {str(e)}"

##https://langchain-ai.github.io/langgraph/tutorials/multi_agent/agent_supervisor/#2-create-supervisor-with-langgraph-supervisor

##https://langchain-ai.github.io/langgraph/how-tos/tool-calling/#update-state

from typing import Annotated
from langchain_core.tools import tool, InjectedToolCallId
from langgraph.prebuilt import InjectedState
from langgraph.graph import StateGraph, START, MessagesState
from langgraph.types import Command


def create_handoff_tool(*, agent_name: str, description: str | None = None):
    name = f"transfer_to_{agent_name}"
    description = description or f"Ask {agent_name} for help."

    @tool(name, description=description)
    def handoff_tool(
        state: Annotated[MessagesState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
    ) -> Command:
        tool_message = {
            "role": "tool",
            "content": f"Successfully transferred to {agent_name}",
            "name": name,
            "tool_call_id": tool_call_id,
        }
        return Command(
            goto=agent_name,  
            update={**state, "messages": state["messages"] + [tool_message]},  
            graph=Command.PARENT,  
        )

    return handoff_tool


# Handoffs
assign_to_research_agent = create_handoff_tool(
    agent_name="research_agent",
    description="Assign task to a researcher agent.",
)

assign_to_math_agent = create_handoff_tool(
    agent_name="math_agent",
    description="Assign task to a math agent.",
)


research_agent = create_react_agent(
    model="openai:gpt-4.1",
    tools=[search_linkedin_profiles],
    prompt=(
        "You are a research agent specialized in LinkedIn profile searches.\n\n"
        "INSTRUCTIONS:\n"
        "- Use the search_linkedin_profiles without further clarification \n"
        "- When the tool succeeds, report the results clearly\n"
    ),
    name="research_agent",
    stream_mode="messages",  # Enable streaming
)

# Create streaming-enabled model
streaming_model = ChatOpenAI(model="gpt-4.1", streaming=True)

supervisor_agent = create_react_agent(
    model=streaming_model,
    tools=[assign_to_research_agent, assign_to_math_agent],
    prompt=(
        "You are a supervisor managing two agents:\n"
        "- a research agent. Assign research-related linkedin tasks to this agent\n"
        "- a math agent. Assign math-related tasks to this agent\n"
        "Assign work to one agent at a time, do not call agents in parallel.\n"
        "Do not do any work yourself."
    ),
    name="supervisor",
    stream_mode="messages",  # Enable streaming
)

from langgraph.graph import END

# Define the multi-agent supervisor graph
supervisor = (
    StateGraph(MessagesState, context_schema=Configuration)
    # NOTE: `destinations` is only needed for visualization and doesn't affect runtime behavior
    .add_node(supervisor_agent, destinations=("research_agent" ,END))
    .add_node(research_agent)
    .add_node(math_agent)
    .add_edge(START, "supervisor")
    # always return back to the supervisor
    .add_edge("research_agent", "supervisor")
    .add_edge("math_agent", "supervisor")
    .compile()
)

# supervisor.invoke(
#     {
#         "messages": [
#             {
#                 "role": "user",
#                 "content": "find the linkedin profiles of the top 100 CEOs in the US?",
#             }
#         ],
       
#     },
#     {"mode":"My"},
  
    
# )
# for chunk in supervisor.stream(
#     {
#         "messages": [
#             {
#                 "role": "user",
#                 "content": "find the linkedin profiles of the top 100 CEOs in the US?",
#             }
#         ],
       
#     },
#     {"mode":"My"},
  
    
# ):
#      pretty_print_messages(chunk, last_message=True)
# final_message_history = chunk["supervisor"]["messages"]

# Invoke the agent with new query
run = False
if run:
    result = supervisor.invoke(
        {
            "messages": [
                {
                    "role": "user",
                    "content": "Find the Linkedin profile for outbond.io",
                }
            ],
            "table_summary": None,
            "mode": "chat",
            "selected_row_ids": None,
            "selected_column_ids": None,
            "intents": None,
            "tool_calls": None,
            "plan_tasks": [],
            "active_task_id": None,
            "next": None,
            "last_error_message": None
        },
        {"configurable": {"table_id": "your_table_id", "model": "openai:gpt-4.1"}}
    )

    # Print the result with better formatting
    print("=== SUPERVISOR INVOKE RESULT ===")
    print("\n--- FINAL MESSAGES ---")
    for i, message in enumerate(result.get("messages", [])):
        print(f"\nMessage {i+1}:")
        print(f"  Type: {type(message).__name__}")
        if hasattr(message, 'name') and message.name:
            print(f"  Name: {message.name}")
        print(f"  Content: {message.content}")
        if hasattr(message, 'tool_calls') and message.tool_calls:
            print(f"  Tool Calls: {message.tool_calls}")

    print(f"\n--- STATE INFO ---")
    print(f"Mode: {result.get('mode', 'N/A')}")
    print(f"Next: {result.get('next', 'N/A')}")
    if result.get('table_summary'):
        print(f"Table Summary: {result['table_summary']}")

    print(f"\n--- RAW RESULT ---")
# import json
# print(json.dumps(result, indent=2, default=str))



