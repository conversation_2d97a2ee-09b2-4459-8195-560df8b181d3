"""
Test for LinkedIn Enrichment Agent.

This test verifies the linkedin_enrichment_agent functionality including:
- Agent initialization and configuration
- Tool availability and execution
- LinkedIn profile enrichment capabilities
- Error handling and validation
"""

import pytest
import sys
import os

# Add the project root to path
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, project_root)

from src.agents.bond_ai.graph import get_node_for_testing
from langchain_core.messages import HumanMessage, AIMessage
from src.tests.shared_test_config import TEST_CONFIG, TABLE_SUMMARY

# Tests are synchronous, no asyncio needed

def test_linkedin_enrichment_messages():
    """Test LinkedIn enrichment agent with message tracing"""
    
    # Get the agent
    linkedin_agent = get_node_for_testing("linkedin_enrichment_agent")
    
    # Create test state
    state = {
        "table_summary": "Test table with LinkedIn URLs",
        "messages": [
            HumanMessage(content="Create a LinkedIn profile column from column 4")
        ]
    }
    
    # Test config
    config = {"configurable": {"table_id": "test_table"}}
    
    try:
        result = linkedin_agent(state, config)
        print("SUCCESS: Agent executed without error")
        print(f"Result: {result}")
    except Exception as e:
        print(f"ERROR: {e}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()
@pytest.mark.skip(reason="Focusing on other tests first")
def test_linkedin_enrichment_agent_initialization():
    """Test that the linkedin_enrichment_agent can be initialized properly."""
    # Get the agent node for testing
    linkedin_enrichment_agent = get_node_for_testing("linkedin_enrichment_agent")
    
   
    # Verify the agent is callable
    assert callable(linkedin_enrichment_agent)
    print("✅ LinkedIn Enrichment Agent initialized successfully")

@pytest.mark.skip(reason="Focusing on other tests first")
def test_linkedin_enrichment_agent_basic_functionality():
    """Test basic functionality of the linkedin_enrichment_agent."""
    # Get the agent node for testing
    linkedin_enrichment_agent = get_node_for_testing("linkedin_enrichment_agent")
    
    # Create test state with a LinkedIn enrichment request
    state = {
        "table_summary": TABLE_SUMMARY,
        "messages": [
            HumanMessage(content="Create a LinkedIn profile enrichment column using the existing LinkedIn Profile URLs in the table. Name the column 'LinkedIn Profile Data'.")
        ]
    }
    
    # Test the agent
    result = linkedin_enrichment_agent(state, TEST_CONFIG)
    
    # Verify the result structure
    assert "messages" in result
    assert len(result["messages"]) > 0
    
    # Check that the agent responded
    last_message = result["messages"][-1]
    assert isinstance(last_message, AIMessage)
    
    print("✅ LinkedIn Enrichment Agent basic functionality test passed")
    print(f"Agent response: {last_message.content[:200]}...")

@pytest.mark.skip(reason="Focusing on other tests first")
def test_linkedin_enrichment_agent_with_tool_calls():
    """Test linkedin_enrichment_agent with tool call scenarios."""
    # Get the agent node for testing
    linkedin_enrichment_agent = get_node_for_testing("linkedin_enrichment_agent")
    
    # Create test state with specific LinkedIn enrichment instructions
    state = {
        "table_summary": TABLE_SUMMARY,
        "messages": [
            HumanMessage(content="""
            I need you to create a new LinkedIn profile enrichment column. 
            Use the column name "LinkedIn Profile Data" and use the LinkedIn Profile URLs from column 4.
            The injection path should be: {{LinkedIn Profile.cell_value}}
            """)
        ]
    }
    
    # Test the agent
    result = linkedin_enrichment_agent(state, TEST_CONFIG)
    
    # Verify the result
    assert "messages" in result
    assert len(result["messages"]) > 0
    
    # Check for tool calls in the response
    last_message = result["messages"][-1]
    
    # The agent should either make tool calls or provide reasoning about the enrichment
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        print("✅ Agent made tool calls as expected")
        for tool_call in last_message.tool_calls:
            print(f"Tool called: {tool_call['name']}")
            print(f"Tool args: {tool_call['args']}")
    else:
        # Agent might be reasoning about the task first
        print("✅ Agent provided reasoning response")
        print(f"Response content: {last_message.content[:300]}...")
    
    print("✅ LinkedIn Enrichment Agent tool call test completed")

@pytest.mark.skip(reason="Focusing on other tests first")
def test_linkedin_enrichment_agent_error_handling():
    """Test linkedin_enrichment_agent error handling with invalid requests."""
    # Get the agent node for testing
    linkedin_enrichment_agent = get_node_for_testing("linkedin_enrichment_agent")
    
    # Create test state with invalid/unclear request
    state = {
        "table_summary": TABLE_SUMMARY,
        "messages": [
            HumanMessage(content="Do something with LinkedIn but I'm not sure what exactly.")
        ]
    }
    
    # Test the agent
    result = linkedin_enrichment_agent(state, TEST_CONFIG)
    
    # Verify the agent handles unclear requests gracefully
    assert "messages" in result
    assert len(result["messages"]) > 0
    
    last_message = result["messages"][-1]
    assert isinstance(last_message, AIMessage)
    
    # The agent should ask for clarification or provide guidance
    response_content = last_message.content.lower()
    assert any(keyword in response_content for keyword in [
        "clarify", "specify", "need more", "unclear", "help", "what", "how"
    ]), "Agent should ask for clarification on unclear requests"
    
    print("✅ LinkedIn Enrichment Agent error handling test passed")
    print(f"Agent response: {last_message.content[:200]}...")

@pytest.mark.skip(reason="Focusing on other tests first")
def test_linkedin_enrichment_agent_context_awareness():
    """Test that the agent is aware of table context and existing columns."""
    # Get the agent node for testing
    linkedin_enrichment_agent = get_node_for_testing("linkedin_enrichment_agent")
    
    # Create test state asking about existing table structure
    state = {
        "table_summary": TABLE_SUMMARY,
        "messages": [
            HumanMessage(content="What LinkedIn-related columns can I create based on the current table data?")
        ]
    }
    
    # Test the agent
    result = linkedin_enrichment_agent(state, TEST_CONFIG)
    
    # Verify the agent is context-aware
    assert "messages" in result
    assert len(result["messages"]) > 0
    
    last_message = result["messages"][-1]
    response_content = last_message.content.lower()
    
    # Agent should reference the existing LinkedIn Profile column
    assert any(keyword in response_content for keyword in [
        "linkedin profile", "column 4", "existing", "current table", "algebris"
    ]), "Agent should be aware of existing table structure"
    
    print("✅ LinkedIn Enrichment Agent context awareness test passed")
    print(f"Agent response: {last_message.content[:200]}...")
@pytest.mark.skip(reason="Focusing on other tests first")
def test_linkedin_enrichment_agent_tool_invocation_debug():
    """Debug test to check tool availability and invocation."""
    # Get the agent node for testing
    linkedin_enrichment_agent = get_node_for_testing("linkedin_enrichment_agent")
    
    # Check if the agent has tools attached
    if hasattr(linkedin_enrichment_agent, '_tools'):
        print(f"✅ Agent has {len(linkedin_enrichment_agent._tools)} tools:")
        for tool in linkedin_enrichment_agent._tools:
            print(f"  - {tool.name}")
    else:
        print("❌ Agent has no tools attached")
    
    # Create a more explicit tool-triggering request
    state = {
        "table_summary": TABLE_SUMMARY,
        "messages": [
            HumanMessage(content="""
            I need you to create a LinkedIn profile enrichment column. 
            
           
            """)
        ]
    }
    
    # Test the agent
    result = linkedin_enrichment_agent(state, TEST_CONFIG)
    
    # Debug the response
    print(f"Result keys: {result.keys()}")
    last_message = result["messages"][-1]
    print(f"Message type: {type(last_message)}")
    print(f"Message content: {last_message.content[:500]}...")
    
    if hasattr(last_message, 'tool_calls'):
        print(f"Tool calls: {last_message.tool_calls}")
    
    return result

if __name__ == "__main__":
    """Run tests directly for debugging."""
    print("🚀 Starting LinkedIn Enrichment Agent Tests")
    print("=" * 60)
    
    try:
        test_linkedin_enrichment_agent_initialization()
        test_linkedin_enrichment_agent_basic_functionality()
        test_linkedin_enrichment_agent_with_tool_calls()
        test_linkedin_enrichment_agent_error_handling()
        test_linkedin_enrichment_agent_context_awareness()
        
        print("\n" + "=" * 60)
        print("🎉 All LinkedIn Enrichment Agent tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

