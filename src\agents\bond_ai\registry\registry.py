
# ============================================================================
# SIMPLE AGENT REGISTRY
# ============================================================================


from enum import Enum
from dataclasses import dataclass
from typing import List, Any, Optional, Required


from bond_ai.tools import upsert_linkedin_person_profile_column_from_url, upsert_linkedin_company_profile_column_from_url
from bond_ai.tools import read_table_data

from bond_ai.tools.build_list import build_people_list_from_linkedin

from bond_ai.tools import upsert_work_email_column, upsert_phone_number_column

from bond_ai.tools import run_column, upsert_text_column, upsert_ai_text_column, upsert_bond_ai_researcher_column, upsert_ai_message_copywriter, read_user_view_table_filters, update_user_view_table_filters_tool


from bond_ai.prompts_v1 import (
    BUILD_LIST_AGENT_PROMPT,
    LINKEDIN_ENRICHMENT_AGENT_PROMPT,
    EMAIL_PHONE_AGENT_PROMPT_TBD,
    CONTENT_AGENT_PROMPT,
    DATA_MANAGEMENT_AGENT_PROMPT,
    EXECUTION_AGENT_PROMPT
)

@dataclass
class AgentInfo:
    """Agent information containing name, description, and tools."""
    name: str
    description: str
    tools: Optional[List[Any]] = None # type: List[Any]
    system_prompt: Optional[str] = None
    state_injections: Optional[dict] = None

class SupervisorSubAgents(Enum):
    """
    Enumeration of specialized agents in the Supervisor Agentic Pattern.
    
    Each agent represents a specific domain of expertise within the outbound sales workflow.
    Agents are designed to work independently while being orchestrated by the supervisor.
    
    Each enum value contains the agent name, description, and associated tools.
    """

    build_list_agent = AgentInfo(
        name="build_list_agent",
        description="Build a prospect list (People or Companies) from LinkedIn", #and other sources.in the future
        tools=[build_people_list_from_linkedin],
        system_prompt=BUILD_LIST_AGENT_PROMPT
    )
    
    linkedin_enrichment_agent = AgentInfo(
        name="linkedin_enrichment_agent", 
        description="Create or update LinkedIn Person Details and LinkedIn Company Details columns",
        tools=[
            upsert_linkedin_person_profile_column_from_url,
            upsert_linkedin_company_profile_column_from_url
        ],
        system_prompt=LINKEDIN_ENRICHMENT_AGENT_PROMPT,
        state_injections={"table_summary": "table_summary"}
    )
    
    email_phone_agent = AgentInfo(
        name="email_phone_agent",
        description="Create or update *phone number* and *work email* columns",
        tools=[
            upsert_phone_number_column,
            upsert_work_email_column
        ],
        system_prompt=EMAIL_PHONE_AGENT_PROMPT_TBD,
        state_injections={"table_summary": "table_summary"}
    )
    
    content_agent = AgentInfo(
        name="content_agent",
        description="Content generation and personalization",
        tools=[
            upsert_text_column,
            upsert_ai_text_column,
            upsert_bond_ai_researcher_column,
            upsert_ai_message_copywriter
        ],
        system_prompt=CONTENT_AGENT_PROMPT
    )
    
    data_management_agent = AgentInfo(
        name="data_management_agent", 
        description="Table data operations and filter management",
        tools=[
            read_table_data,
            read_user_view_table_filters,
            update_user_view_table_filters_tool
        ],
        system_prompt=DATA_MANAGEMENT_AGENT_PROMPT
    )
    
    execution_agent = AgentInfo(
        name="execution_agent",
        description="Column execution with human-in-the-loop confirmation",
        tools=[run_column],
        system_prompt=EXECUTION_AGENT_PROMPT
    )
    #custom agent nodes
    icp_agent = AgentInfo(
        name="icp_agent",
        description="Creates comprehensive ICP and personas with proper completion handling",
        
    )
    planner_agent = AgentInfo(
        name="planner_agent",
        description="Creates comprehensive plans for complex tasks"
    )
    
    
    
    # Future/deprecated agents
    #  agent_message_copy = "agent_message_copy"
    #   agent_action_table = "agent_action_table"
    #   agent_upload_csv = "agent_upload_csv"
    #   agent_http_column = "agent_http_column"
    #   agent_webhook_column = "agent_webhook_column"
    #   agent_formula = "agent_formula"
    #   agent_clean_up_column = "agent_clean_up_column"





SUPERVISOR_SUB_AGENT_TOOLS_MAP_DEPREACTED = {
    # Tool mapping for the Supervisor Agentic Pattern.
    
    # Maps each specialized agent to their domain-specific tools, ensuring proper
    # separation of concerns and preventing tool conflicts between agents.
    
    # Design Principles:
    # - Each tool belongs to exactly one agent (no overlap)
    # - Tools are grouped by functional domain
    # - Agent selection determines available tool set
    # - Supervisor uses this mapping for delegation decisions
    
    # Tool Categories:
    # - List Building: LinkedIn prospect discovery
    # - Enrichment: Profile data import and enhancement
    # - Contact Discovery: Email and phone number finding
    # - Content: AI-powered content generation and research
    # - Data Management: Table operations and filtering
    # - Execution: Column running with user confirmation
    
    # Prospect Discovery Agent
    # Specializes in building prospect lists from external sources
    SupervisorSubAgents.build_list_agent: [
        build_people_list_from_linkedin,                           # Create list of people from LinkedIn
        # Future: LinkedIn post analysis, company discovery
    ],
    
    # LinkedIn Enrichment Agent  
    # Handles LinkedIn profile data import for both people and companies
    SupervisorSubAgents.linkedin_enrichment_agent: [
        upsert_linkedin_person_profile_column_from_url,            # Create or update LinkedIn person profile details column
        upsert_linkedin_company_profile_column_from_url,           # Create or update LinkedIn company profile details column
    ],

    # Contact Discovery Agent
    # Specializes in finding email addresses and phone numbers
    SupervisorSubAgents.email_phone_agent: [
        upsert_phone_number_column,                                # Create or update phone number column
        upsert_work_email_column                                   # Create or update work email column
    ], 
    
    # Content Generation Agent
    # Handles all content creation, research, and personalization
    SupervisorSubAgents.content_agent: [
        upsert_text_column,                                        # Static text/formula columns
        upsert_ai_text_column,                                     # AI-generated content
        upsert_bond_ai_researcher_column,                          # Research insights
        upsert_ai_message_copywriter                               # Personalized messaging
    ],
    
    # Data Management Agent
    # Manages table operations, filtering, and data analysis
    SupervisorSubAgents.data_management_agent: [
        read_table_data,                                           # Table data retrieval
        read_user_view_table_filters,                              # Filter state reading
        update_user_view_table_filters_tool                        # Filter modifications
    ],
    
    # Execution Agent
    # Handles column execution with mandatory human-in-the-loop confirmation
    SupervisorSubAgents.execution_agent: [  # HITL ALWAYS
       run_column                                                  # Column execution with user approval
    ],
}



class SupervisorSubagentRegistry:
    """Simple registry for managing agent configurations."""

    def __init__(self):
        self.agents = {}
        # self.tools = {}
        self.nodes = {}  # Store pre-built node functions

    def register_react_prebuild_agent(self, name: str, system_prompt: str, tools: list, description: str, enabled: bool = True, state_injections: dict = None, **kwargs):
        """Register a react prebuild agent with optional state injection."""
        self.agents[name] = {
            "system_prompt": system_prompt,
            "tools": tools,
            "enabled": enabled,
            "description": description,
            "type": "react_agent",
            "state_injections": state_injections or {},
            **kwargs
        }
        return self

    def register_custom_agent(self, name: str, node_function, description: str, enabled: bool = True, **kwargs):
        """Register a custom agent."""
        self.agents[name] = {
            #"system prompt"
            "node_function": node_function,
            #"tools"
            "enabled": enabled,
            "description": description,
            "type": "custom_node",
         
            **kwargs
        }
        return self

    # def register_tool(self, name: str, tool):
    #     """Register a tool by name."""
    #     self.tools[name] = tool
    #     return self

    ## SUPPORT FUNCTION - TO EVALUATE what is required
    def get_enabled_agents(self):
        """Get all enabled agent configurations."""
        return {name: config for name, config in self.agents.items() if config.get("enabled", True)}

    def get_agent_names(self):
        """Get list of enabled agent names."""
        return list(self.get_enabled_agents().keys())

    def get_react_agents(self):
        """Get only ReAct agents (not custom nodes)."""
        return {name: config for name, config in self.get_enabled_agents().items()
                if config.get("type") == "react_agent"}

    def get_custom_nodes(self):
        """Get only custom node functions."""
        return {name: config for name, config in self.get_enabled_agents().items()
                if config.get("type") == "custom_node"}

    def enable_agent(self, name: str):
        """Enable an agent."""
        if name in self.agents:
            self.agents[name]["enabled"] = True

    def disable_agent(self, name: str):
        """Disable an agent."""
        if name in self.agents:
            self.agents[name]["enabled"] = False

# Create global registry
supervisor_sub_agent_registry = SupervisorSubagentRegistry()
