import pytest
import sys
import os

# Add the project root to path
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, project_root)

from src.agents.bond_ai.graph import get_node_for_testing
from langgraph.types import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage
from src.agents.bond_ai.state import Task

# Configure pytest-asyncio
pytestmark = pytest.mark.asyncio

@pytest.mark.asyncio
async def test_supervisor_node_with_no_plan():
    """Test supervisor node when no execution plan exists."""
    supervisor_node = get_node_for_testing("supervisor")
    
    state = {
        "table_summary": "Test table with marketing data",
        "messages": [
            HumanMessage(content="Find marketing managers and create outreach messages.")
        ]
    }
    
    test_config = RunnableConfig(
        configurable={
            "table_id": "tbl_c19353f2f59e4786",
            "model": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0"
        }
    )
    
    result = await supervisor_node(state, test_config)
    
    assert "messages" in result or "active_agent" in result or "next" in result
    print(f"Supervisor result (no plan): {result}")
    
    # Note: you can also simply use assertion to show result in test output without using print
    # assert result is not None, f"Supervisor result (no plan): {result}"

# @pytest.mark.asyncio
# async def test_supervisor_node_with_execution_plan():
#     """Test supervisor node with existing execution plan."""
#     supervisor_node = get_node_for_testing("supervisor")
    
#     sample_tasks = [
#         Task(
#             id="task_1",
#             order=1,
#             action="Research marketing managers",
#             agent="research_agent",
#             tool="lima_search",
#             why="To find target prospects",
#             status="pending"
#         ),
#         Task(
#             id="task_2", 
#             order=2,
#             action="Enrich prospect data",
#             agent="enrichment_agent",
#             tool="linkedin_enrichment",
#             why="To gather detailed information",
#             status="pending"
#         )
#     ]
    
#     state = {
#         "table_summary": "Test table with marketing data",
#         "messages": [
#             HumanMessage(content="Find marketing managers and create outreach messages.")
#         ],
#         "execution_plan": {
#             "id": "plan_123",
#             "user_request": "Find marketing managers and create outreach messages",
#             "tasks": [task.model_dump() for task in sample_tasks]
#         },
#         "plan_tasks": sample_tasks
#     }
    
#     test_config = RunnableConfig(
#         configurable={
#             "table_id": "tbl_test",
#             "model": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0"
#         }
#     )
    
#     result = await supervisor_node(state, test_config)
    
#     assert "messages" in result or "active_agent" in result or "next" in result
#     print(f"Supervisor result (with plan): {result}")

# @pytest.mark.asyncio
# async def test_supervisor_node_task_completion():
#     """Test supervisor node when tasks are completed."""
#     supervisor_node = get_node_for_testing("supervisor")
    
#     completed_tasks = [
#         Task(
#             id="task_1",
#             order=1,
#             action="Research marketing managers",
#             agent="research_agent", 
#             tool="lima_search",
#             why="To find target prospects",
#             status="completed"
#         ),
#         Task(
#             id="task_2",
#             order=2,
#             action="Enrich prospect data",
#             agent="enrichment_agent",
#             tool="linkedin_enrichment", 
#             why="To gather detailed information",
#             status="completed"
#         )
#     ]
    
#     state = {
#         "table_summary": "Test table with marketing data",
#         "messages": [
#             HumanMessage(content="Find marketing managers and create outreach messages."),
#             AIMessage(content="Research completed successfully"),
#             AIMessage(content="Enrichment completed successfully")
#         ],
#         "execution_plan": {
#             "id": "plan_123",
#             "user_request": "Find marketing managers and create outreach messages",
#             "tasks": [task.model_dump() for task in completed_tasks]
#         },
#         "plan_tasks": completed_tasks
#     }
    
#     test_config = RunnableConfig(
#         configurable={
#             "table_id": "tbl_test",
#             "model": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0"
#         }
#     )
    
#     result = await supervisor_node(state, test_config)
    
#     assert "messages" in result or "next" in result
#     print(f"Supervisor result (completed tasks): {result}")

# @pytest.mark.asyncio
# async def test_supervisor_node_error_handling():
#     """Test supervisor node with error conditions."""
#     supervisor_node = get_node_for_testing("supervisor")
    
#     error_task = Task(
#         id="task_1",
#         order=1,
#         action="Research marketing managers",
#         agent="research_agent",
#         tool="lima_search", 
#         why="To find target prospects",
#         status="failed",
#         error="API rate limit exceeded"
#     )
    
#     state = {
#         "table_summary": "Test table with marketing data",
#         "messages": [
#             HumanMessage(content="Find marketing managers and create outreach messages."),
#             AIMessage(content="Error occurred during research")
#         ],
#         "execution_plan": {
#             "id": "plan_123",
#             "user_request": "Find marketing managers and create outreach messages",
#             "tasks": [error_task.model_dump()]
#         },
#         "plan_tasks": [error_task],
#         "last_error_message": "API rate limit exceeded"
#     }
    
#     test_config = RunnableConfig(
#         configurable={
#             "table_id": "tbl_test",
#             "model": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0"
#         }
#     )
    
#     result = await supervisor_node(state, test_config)
    
#     assert "messages" in result or "active_agent" in result or "next" in result
#     print(f"Supervisor result (error handling): {result}")

# @pytest.mark.asyncio
# async def test_supervisor_node_mixed_task_states():
#     """Test supervisor node with mixed task completion states."""
#     supervisor_node = get_node_for_testing("supervisor")
    
#     mixed_tasks = [
#         Task(
#             id="task_1",
#             order=1,
#             action="Research marketing managers",
#             agent="research_agent",
#             tool="lima_search",
#             why="To find target prospects", 
#             status="completed"
#         ),
#         Task(
#             id="task_2",
#             order=2,
#             action="Enrich prospect data",
#             agent="enrichment_agent",
#             tool="linkedin_enrichment",
#             why="To gather detailed information",
#             status="in_progress"
#         ),
#         Task(
#             id="task_3",
#             order=3,
#             action="Create outreach messages",
#             agent="content_agent",
#             tool="content_generation",
#             why="To personalize communication",
#             status="pending"
#         )
#     ]
    
#     state = {
#         "table_summary": "Test table with marketing data",
#         "messages": [
#             HumanMessage(content="Find marketing managers and create outreach messages."),
#             AIMessage(content="Research completed successfully")
#         ],
#         "execution_plan": {
#             "id": "plan_123", 
#             "user_request": "Find marketing managers and create outreach messages",
#             "tasks": [task.model_dump() for task in mixed_tasks]
#         },
#         "plan_tasks": mixed_tasks
#     }
    
#     test_config = RunnableConfig(
#         configurable={
#             "table_id": "tbl_test",
#             "model": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0"
#         }
#     )
    
#     result = await supervisor_node(state, test_config)
    
#     assert "messages" in result or "active_agent" in result or "next" in result
#     print(f"Supervisor result (mixed states): {result}")

# async def run_tests():
#     """Run all tests asynchronously."""
#     print("🚀 Starting Supervisor Node Tests")
#     print("=" * 50)
    
#     test_functions = [
#         test_supervisor_node_with_no_plan,
#         test_supervisor_node_with_execution_plan,
#         test_supervisor_node_task_completion,
#         test_supervisor_node_error_handling,
#         test_supervisor_node_mixed_task_states
#     ]
    
#     passed = 0
#     failed = 0
    
#     for test_func in test_functions:
#         try:
#             print(f"\n🧪 Running {test_func.__name__}")
#             await test_func()
#             print("✅ PASSED")
#             passed += 1
#         except Exception as e:
#             print(f"❌ FAILED: {str(e)}")
#             import traceback
#             traceback.print_exc()
#             failed += 1
    
#     print(f"\n📊 Test Summary: {passed} passed, {failed} failed")

# if __name__ == "__main__":
#     asyncio.run(run_tests())


