

How to run

 uv run pytest src/tests/test_supervisor.py -v 


With the print diplayed
uv run pytest src/tests/test_supervisor.py -v  -s 


uv run pytest src/tests/test_supervisor.py -v -s --disable-warnings

[tool.pytest.ini_options]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
    "ignore::PendingDeprecationWarning",
]


[tool.pytest.ini_options]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
    "ignore::PendingDeprecationWarning",
]