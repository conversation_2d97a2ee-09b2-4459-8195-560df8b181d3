

from langchain_core.messages import SystemMessage,HumanMessage
from langchain_core.runnables import RunnableConfig


from datetime import datetime

from bond_ai.utils import load_chat_model, clean_thinking_blocks_for_bedrock
from bond_ai.configuration import Configuration
from bond_ai.state import BondAIWorkflowState
from bond_ai.tools.tools import all_tools
from ..agent_db import get_table_filters

from  bond_ai.nodes import summarize_selected_rows

from bond_ai.prompts import OUTBOND_AI_ASSISTANT_PROMPT


#from src.outbond_ai_assistant_v3.tools.tools import tools_store,tool_registry


# def get_relevant_tools(state, limit=5):
#     """Extract user intent and find relevant tools via semantic search."""
#     try:
#         # Extract query from conversation context (last 2-3 messages)
#         messages = state["messages"]
#         query_parts = []
        
#         for msg in reversed(messages[-3:]):  # Look at last 3 messages
#             if isinstance(msg, HumanMessage) and msg.content.strip():
#                 query_parts.append(msg.content.strip())
        
#         if query_parts:
#             query = " ".join(reversed(query_parts))  # Maintain chronological order
#         else:
#             # Fallback to all tools if no clear intent
#             return tools
        
#         # Perform semantic search
#         search_results = tools_store.search(("tools",), query=query, limit=limit)
        
#         if not search_results:
#             # Fallback to all tools if search fails
#             return tools
        
#         # Build relevant tools list
#         relevant_tools = []
#         for result in search_results:
#             tool_id = result.key
#             if tool_id in tool_registry:
#                 relevant_tools.append(tool_registry[tool_id])
        
#         # Ensure minimum tool availability
#         return relevant_tools if len(relevant_tools) >= 3 else tools
        
#     except Exception as e:
#         print(f"Tool selection error: {e}")
#         return tools  # Safe fallback to all tools



def call_model(
    state: BondAIWorkflowState,
    config: RunnableConfig,
):
    """Node that calls the LLM to generate the next response.

    This is the core reasoning node of the ReAct agent that processes user messages and generates
    responses using a language model. The node handles both chat mode (conversational responses only)
    and tool mode (can use tools to perform actions).

    Architecture:
        - Loads and configures the appropriate language model based on configuration
        - Binds available tools to the model (except in chat mode)
        - Constructs comprehensive system prompts with table context
        - Handles selected columns/rows context
        - Processes conversation history and generates responses
        - Includes error handling with fallback responses

    State Processing:
        - Reads current conversation messages from state
        - Incorporates table summary and filter information
        - Adds context for selected columns and rows when available
        - Cleans thinking blocks from messages for model compatibility

    Tool Integration:
        The model has access to 15 specialized tools when not in chat mode:

        1. **search** - Web search using Tavily search engine
           - Parameters: query (str), max_results (int, default=5)
           - Returns: List of search results with URLs, titles, and content
           - Use case: Research current events, find general information

        2. **scrape_website** - Extract content from web pages
           - Parameters: url (str)
           - Returns: Clean markdown content from the webpage
           - Use case: Get detailed information from specific websites

        3. **read_table_data** - Fetch and analyze table data
           - Parameters: max_rows (int), filters (FilterGroup), search (str),
                        sorts (List[Sort]), column_ids (List[int]), summarize (bool)
           - Returns: Tuple of table data and summary
           - Use case: Read current table contents, apply filters, search data

        4. **upsert_linkedin_person_profile_column_from_url** - LinkedIn profile enrichment
           - Parameters: column_name (str), linkedin_profile_url (str), column_id (int, optional)
           - Returns: Success/error tuple
           - Use case: Create/update columns that fetch LinkedIn profile data from URLs

        5. **upsert_linkedin_company_profile_column_from_url** - LinkedIn company enrichment
           - Parameters: column_name (str), linkedin_company_url (str), column_id (int, optional)
           - Returns: Success/error tuple
           - Use case: Create/update columns that fetch LinkedIn company data from URLs

        6. **upsert_phone_number_column** - Phone number enrichment
           - Parameters: column_name (str), linkedin_profile_url (str), column_id (int, optional)
           - Returns: Success/error tuple
           - Use case: Create/update columns that find phone numbers from LinkedIn profiles

        7. **upsert_work_email_column** - Work email enrichment
           - Parameters: column_name (str), full_name (str), company_domain (str), column_id (int, optional)
           - Returns: Success/error tuple
           - Use case: Create/update columns that find work emails using name and company domain

        8. **upsert_text_column** - Text/formula columns
           - Parameters: column_name (str), text_formula (str), column_id (int, optional)
           - Returns: Success/error tuple
           - Use case: Create/update columns with static text or formulas combining other columns

        9. **upsert_ai_text_column** - AI-powered text generation
           - Parameters: column_name (str), prompt (str), required_fields (List[str]),
                        run_condition (str, optional), column_id (int, optional)
           - Returns: Success/error tuple
           - Use case: Create/update columns that generate AI content based on other column data

        10. **upsert_bond_ai_researcher_column** - AI research columns
            - Parameters: column_name (str), prompt (str), required_fields (List[str]),
                         run_condition (str, optional), column_id (int, optional)
            - Returns: Success/error tuple
            - Use case: Create/update columns that perform online research based on table data

        11. **upsert_ai_message_copywriter** - AI message generation
            - Parameters: column_name (str), prompt (str), required_fields (List[str]),
                         run_condition (str, optional), column_id (int, optional)
            - Returns: Success/error tuple
            - Use case: Create/update columns that generate personalized outreach messages

        12. **run_column** - Execute column processing
            - Parameters: column_id (str), column_name (str), count (int, default=1),
                         row_id (int, default=1), wait_results (bool, default=False)
            - Returns: Success/error tuple with execution results
            - Use case: Trigger processing of smart columns, requires user confirmation

        13. **search_linkedin_profiles** - LinkedIn profile search
            - Parameters: filters (List[LinkedInFilterType]), page (int, default=1)
            - Returns: Search results and profile data
            - Use case: Search for LinkedIn profiles using advanced filters, build prospect lists

        14. **read_user_view_table_filters** - Read table filters
            - Parameters: config (injected)
            - Returns: Current table filter configuration
            - Use case: Check current filtering settings on the table

        15. **update_user_view_table_filters_tool** - Update table filters
            - Parameters: filters (FilterGroup, optional)
            - Returns: Updated filter configuration
            - Use case: Modify how data is filtered in table views

    Mode Handling:
        - **Chat Mode**: When state["mode"] == "chat"
          - Model operates without tools (conversational only)
          - System prompt includes chat mode instructions
          - Suitable for Q&A, guidance, and explanations

        - **Tool Mode**: Default mode
          - Model has access to all 15 tools
          - Can perform actions like creating columns, running processes, searching
          - Suitable for data manipulation and automation tasks

    Context Enhancement:
        - **Table Summary**: Includes comprehensive table structure and column descriptions
        - **Current Filters**: Shows active filtering conditions
        - **Selected Columns**: Highlights user-selected columns for focused attention
        - **Selected Rows**: Provides summary of user-selected rows when available
        - **Timestamp**: Includes current date/time for temporal context

    Error Handling:
        - Graceful fallback on model errors
        - Maintains conversation context during errors
        - Provides helpful error messages to users
        - Continues operation with reduced message history if needed

    Returns:
        dict: Contains "messages" key with the model's response as an AIMessage
              The response may include tool calls that will be processed by tool_node

    Dependencies:
        - Configuration: Table ID, model selection
        - State: Conversation history, mode, selected items, table summary
        - Tools: All 15 specialized tools for data operations
        - Prompts: System prompt templates with dynamic content injection
    """
    # Get configuration
    configuration = Configuration.from_runnable_config(config)
    
    # Check if we're in chat mode - if so, don't bind tools
    is_chat_mode = state.get("mode") == "chat"
    
    if is_chat_mode:
        model = load_chat_model(configuration.model)
    else:
        #relevant_tools = get_relevant_tools(state)
        #model = load_chat_model(configuration.model).bind_tools(relevant_tools)
        model = load_chat_model(configuration.model).bind_tools(all_tools)
    
    current_filters = get_table_filters(configuration.table_id)

    # Clean thinking blocks from messages to remove internal metadata
    cleaned_messages = clean_thinking_blocks_for_bedrock(list(state["messages"]))

    # Create the system prompt with table summary
    system_prompt = OUTBOND_AI_ASSISTANT_PROMPT.format(
        table_id=configuration.table_id, 
        today_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 
        current_filters=current_filters,
        table_summary=state["table_summary"]
    )
    
    # Add chat mode information to the prompt if in chat mode
    if is_chat_mode:
        system_prompt += "\n\n**CHAT MODE ACTIVE**: You are currently in chat mode. Answer questions and provide guidance, but do not use any tools or perform any actions. Only provide conversational responses and advice. If the USER wants you to use tools, ask them to turn off chat mode."
    
    # Add context for selected columns if they exist
    selected_column_ids = state.get("selected_column_ids")
    if selected_column_ids:
        # Handle both string and list formats
        if isinstance(selected_column_ids, str):
            column_ids_str = selected_column_ids
        else:
            column_ids_str = str(selected_column_ids)
        
        system_prompt += f"\n\n**SELECTED COLUMNS**: The user has selected specific columns (IDs: {column_ids_str}). Focus your attention and responses on these selected columns when relevant."
    
    # Add context for selected rows if they exist
    selected_row_ids = state.get("selected_row_ids")
    if selected_row_ids:
        try:
            row_summary = summarize_selected_rows(selected_row_ids, config)
            system_prompt += f"\n\n**SELECTED ROWS**: The user has selected specific rows. {row_summary}"
        except Exception as e:
            # Handle any errors in row summarization gracefully
            system_prompt += f"\n\n**SELECTED ROWS**: The user has selected specific rows (IDs: {selected_row_ids}), but unable to fetch summary: {str(e)}"

    # Create the messages with cleaned conversation history
    messages = [
        SystemMessage(system_prompt),
        *cleaned_messages
    ]

    try:
        # Call the model with the current state
        response = model.invoke(messages, config)
         # Track tool calls in state
        tool_calls_to_track = []
        if hasattr(response, 'tool_calls') and response.tool_calls:
            tool_calls_to_track = [
                {
                    "name": tc.get("name"),
                    "args": tc.get("args"),
                    "id": tc.get("id")
                } for tc in response.tool_calls
            ]
        # Return the response to be added to messages
        return {
            "messages": [response],
            "tool_calls": tool_calls_to_track
        }
    except Exception as e:
        # Handle model errors gracefully with same model configuration
        # Clean the last few messages before fallback call
        cleaned_fallback_messages = clean_thinking_blocks_for_bedrock(list(state["messages"][-3:]))
        fallback_messages = [
            SystemMessage(f"There was an error with the previous request. Please provide a helpful response. Error: {str(e)}"),
            *cleaned_fallback_messages
        ]
        fallback_response = model.invoke(fallback_messages, config)
        return {"messages": [fallback_response]}

